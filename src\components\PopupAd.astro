---
let isVisible = true;
let currentImageIndex = 0;
const images = [
  '/images/popup/PROMOTION_V2_1.png',
  '/images/popup/PROMOTION_V2_2.png',
  '/images/popup/PROMOTION_V2_3.png'
];

function closePopup() {
  isVisible = false;
}

function nextImage() {
  currentImageIndex = (currentImageIndex + 1) % images.length;
}

function prevImage() {
  currentImageIndex = (currentImageIndex - 1 + images.length) % images.length;
}
---

{isVisible && (
  <>
    <div class="popup-overlay">
      <div class="popup-content">
        <img src={images[currentImageIndex]} alt="Promotion" class="popup-image" />
        <div class="popup-controls">
          <button class="nav-button prev-button">❮</button>
          <button class="nav-button next-button">❯</button>
        </div>
        <button class="close-button">×</button>
        {/* <div class="click-to-enlarge-hint">Click image to enlarge</div> */}
      </div>
    </div>

    <!-- Enlarged image view - separate from popup -->
    <div class="enlarged-view">
      <div class="enlarged-content">
        <img src={images[currentImageIndex]} alt="Promotion" class="enlarged-image" />
        <button class="close-enlarged-button">×</button>
      </div>
    </div>
  </>
)}

<style>
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  backdrop-filter: blur(5px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  opacity: 1;
  visibility: visible;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.popup-overlay.hidden {
  opacity: 0;
  visibility: hidden;
}

.popup-content {
  background: transparent;
  padding: 40px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 1200px;
  margin: 0 20px;
}

.popup-image {
  max-width: 80%;
  height: auto;
  max-height: 85vh;
  object-fit: contain;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  transition: transform 0.3s ease;
}

.popup-image:hover {
  transform: scale(1.02);
}

.click-to-enlarge-hint {
  position: absolute;
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 14px;
  opacity: 0.8;
  pointer-events: none;
}

/* Enlarged view styles */
.enlarged-view {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.enlarged-view.active {
  opacity: 1;
  visibility: visible;
}

.enlarged-content {
  position: relative;
  width: 90%;
  height: 90%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.enlarged-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  pointer-events: auto; /* Allow interactions with the image */
}

.close-enlarged-button {
  position: absolute;
  top: -40px;
  right: 0;
  background: rgba(255, 0, 0, 0.7);
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  font-size: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  pointer-events: auto; /* Allow interactions with the close button */
  transition: all 0.3s ease;
}

.close-enlarged-button:hover {
  background: rgba(255, 0, 0, 1);
  transform: scale(1.1);
}

.popup-controls {
  position: absolute;
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
  pointer-events: none;
}

.nav-button {
  background: rgba(0, 0, 0, 0.6);
  color: white;
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  font-size: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  pointer-events: auto;
  padding: 0;
  transition: all 0.3s ease;
  opacity: 0.7;
  margin: 0 10px;
}

.nav-button:hover {
  background: rgba(0, 0, 0, 0.9);
  opacity: 1;
  transform: scale(1.1);
}

.close-button {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(255, 0, 0, 0.7);
  color: white;
  border: none;
  border-radius: 50%;
  width: 35px;
  height: 35px;
  font-size: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 0;
  transition: all 0.3s ease;
  opacity: 0.8;
  z-index: 1;
}

.close-button:hover {
  background: rgba(255, 0, 0, 1);
  opacity: 1;
  transform: scale(1.1);
}

@media (max-width: 768px) {
  .popup-content {
    padding: 20px;
  }

  .popup-image {
    max-width: 95%;
  }

  .nav-button {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }

  .close-button {
    width: 30px;
    height: 30px;
    font-size: 18px;
    top: 5px;
    right: 5px;
  }
}
</style>

<script>
  let slideInterval: number | null = null;
  const SLIDE_INTERVAL_TIME = 4000; // 4 seconds

  function startAutoSlide() {
    if (!slideInterval) {
      slideInterval = setInterval(() => {
        const images = ['/images/popup/PROMOTION_V2_1.png', '/images/popup/PROMOTION_V2_2.png', '/images/popup/PROMOTION_V2_3.png'];
        const imgElement = document.querySelector('.popup-image') as HTMLImageElement;
        if (imgElement) {
          let currentIndex = images.indexOf(imgElement.src.split(window.location.origin)[1]);
          currentIndex = (currentIndex + 1) % images.length;
          imgElement.src = images[currentIndex];
        }
      }, SLIDE_INTERVAL_TIME);
    }
  }

  function stopAutoSlide() {
    if (slideInterval) {
      clearInterval(slideInterval);
      slideInterval = null;
    }
  }

  // Start auto-slide when component mounts and ensure popup is visible
  document.addEventListener('DOMContentLoaded', () => {
    // Make sure the popup is visible when the page loads
    const popupOverlay = document.querySelector('.popup-overlay');
    if (popupOverlay) {
      popupOverlay.classList.remove('hidden');
    }
    startAutoSlide();

    // Add click event for enlarging the image
    const popupImage = document.querySelector('.popup-image') as HTMLImageElement;
    const enlargedView = document.querySelector('.enlarged-view') as HTMLElement;
    const enlargedImage = document.querySelector('.enlarged-image') as HTMLImageElement;
    const closeEnlargedButton = document.querySelector('.close-enlarged-button') as HTMLButtonElement;

    if (popupImage && enlargedView && enlargedImage) {
      // Click on popup image to enlarge
      popupImage.addEventListener('click', () => {
        // Stop auto-slide when viewing enlarged image
        stopAutoSlide();

        // Update enlarged image src to match current popup image
        enlargedImage.src = popupImage.src;

        // Show enlarged view
        enlargedView.classList.add('active');
      });

      // Close enlarged view when clicking the close button
      if (closeEnlargedButton) {
        closeEnlargedButton.addEventListener('click', () => {
          enlargedView.classList.remove('active');
          // Restart auto-slide when closing enlarged view
          startAutoSlide();
        });
      }

      // Close enlarged view when clicking outside the image
      enlargedView.addEventListener('click', (event) => {
        // Close if clicking on the overlay background (not on the content area)
        if (event.target === enlargedView) {
          enlargedView.classList.remove('active');
          // Restart auto-slide when closing enlarged view
          startAutoSlide();
        }
      });

      // Close enlarged view with Escape key
      document.addEventListener('keydown', (event) => {
        if (event.key === 'Escape' && enlargedView.classList.contains('active')) {
          enlargedView.classList.remove('active');
          startAutoSlide();
        }
      });
    }
  });

  // Add click handler for closing popup when clicking outside
  document.addEventListener('DOMContentLoaded', () => {
    const popupOverlay = document.querySelector('.popup-overlay');
    const popupContent = document.querySelector('.popup-content');

    if (popupOverlay && popupContent) {
      // Close popup when clicking on the overlay (outside the content)
      popupOverlay.addEventListener('click', (event) => {
        if (event.target === popupOverlay) {
          popupOverlay.classList.add('hidden');
          stopAutoSlide();
        }
      });
    }
  });

  document.addEventListener('click', (event) => {
    const target = event.target as HTMLElement;
    if (target && target.matches('.close-button')) {
      // Instead of setting display: none, we'll use a class to hide it
      // This ensures it will reappear on page refresh
      const popupOverlay = document.querySelector('.popup-overlay') as HTMLElement;
      if (popupOverlay) {
        popupOverlay.classList.add('hidden');
      }
      stopAutoSlide();
    } else if (target && target.matches('.next-button')) {
      stopAutoSlide(); // Stop auto-slide when user interacts
      const images = ['/images/popup/PROMOTION_V2_1.png', '/images/popup/PROMOTION_V2_2.png', '/images/popup/PROMOTION_V2_3.png'];
      const imgElement = document.querySelector('.popup-image') as HTMLImageElement;
      if (imgElement) {
        let currentIndex = images.indexOf(imgElement.src.split(window.location.origin)[1]);
        currentIndex = (currentIndex + 1) % images.length;
        imgElement.src = images[currentIndex];
      }
    } else if (target && target.matches('.prev-button')) {
      stopAutoSlide(); // Stop auto-slide when user interacts
      const images = ['/images/popup/PROMOTION_V2_1.png', '/images/popup/PROMOTION_V2_2.png', '/images/popup/PROMOTION_V2_3.png'];
      const imgElement = document.querySelector('.popup-image') as HTMLImageElement;
      if (imgElement) {
        let currentIndex = images.indexOf(imgElement.src.split(window.location.origin)[1]);
        currentIndex = (currentIndex - 1 + images.length) % images.length;
        imgElement.src = images[currentIndex];
      }
    }
  });
</script>