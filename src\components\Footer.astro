---
---

<footer class="footer">
  <div class="container">
    <div class="footer-content">
      <div class="footer-logo">
        <a href="/" class="footer-logo-link">
          <img src="/images/heropag/JT.png" alt="Jupiter Tech Logo" class="footer-logo-image" width="60" height="60" />
          <span class="logo-text">Jupiter<span class="highlight">Tech</span></span>
        </a>
        <div class="contact-info">
          <div class="contact-item">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
            </svg>
            <a href="mailto:<EMAIL>"><EMAIL></a>
          </div>
          <div class="contact-item">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z"/>
            </svg>
            <a href="tel:0917941108">************</a>
          </div>
        </div>

        <div class="social-icons">
          <a href="https://www.facebook.com/jupiter.tech89" target="_blank" class="social-icon" aria-label="Facebook">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2.04C6.5 2.04 2 6.53 2 12.06C2 17.06 5.66 21.21 10.44 21.96V14.96H7.9V12.06H10.44V9.85C10.44 7.34 11.93 5.96 14.22 5.96C15.31 5.96 16.45 6.15 16.45 6.15V8.62H15.19C13.95 8.62 13.56 9.39 13.56 10.18V12.06H16.34L15.89 14.96H13.56V21.96C18.34 21.21 22 17.06 22 12.06C22 6.53 17.5 2.04 12 2.04Z" />
            </svg>
          </a>
          <a href="https://line.me/ti/p/~@jupitertech" target="_blank" class="social-icon" aria-label="Line">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M22 10.5c0-4.65-4.7-8.42-10.5-8.42S1 5.85 1 10.5c0 4.15 3.7 7.63 8.7 8.3.33.07.79.22.9.5.1.27.07.7.03 1 0 0-.12.72-.14.87-.04.31-.2 1.22 1.08.67 1.27-.56 6.84-4.02 9.32-6.89 1.72-1.88 2.11-3.8 2.11-4.45z"/>
            </svg>
          </a>
          <a href="https://www.tiktok.com/@jupitertech.co.th" target="_blank" class="social-icon" aria-label="TikTok">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/>
            </svg>
          </a>
        </div>
      </div>

      <div class="footer-links">
        <div class="footer-column">
          <h3>บริษัท</h3>
          <ul>
            <li><a href="#" class="home-link">หน้าแรก</a></li>
            <li><a href="#services">ผลิตภัณฑ์ของเรา</a></li>
            <li><a href="#login-system">เข้าสู่ระบบ</a></li>
            <li><a href="#contact">ติดต่อเรา</a></li>
          </ul>
        </div>

        <div class="footer-column">
          <h3>บริการของเรา</h3>
          <ul>
            <li><a href="#services">Total Printing</a></li>
            <li><a href="#services">Platform Tigra</a></li>
            <li><a href="#services">Advanced Label</a></li>
            <li><a href="#services">U-Check</a></li>
          </ul>
        </div>

        <div class="footer-column">
          <h3>ระบบของเรา</h3>
          <ul>
            <li><a href="#">J-Track</a></li>
            <li><a href="https://u-check.jupitertech.co.th/">U-Check</a></li>
            <li><a href="https://thaigarbage.com">Platform Tigra</a></li>
          </ul>
        </div>
      </div>
    </div>

    <div class="footer-bottom">
      <div class="copyright">
        <p>&copy; 2024 Jupiter Tech. All rights reserved.</p>
      </div>
    </div>
  </div>

  <button id="scrollToTop" aria-label="Scroll to top">
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 15.75l7.5-7.5 7.5 7.5" />
    </svg>
  </button>
</footer>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const scrollToTopBtn = document.getElementById('scrollToTop');

    // Show/hide scroll to top button
    window.addEventListener('scroll', () => {
      if (window.scrollY > 300) {
        scrollToTopBtn?.classList.add('visible');
      } else {
        scrollToTopBtn?.classList.remove('visible');
      }
    });

    // Scroll to top when button is clicked
    scrollToTopBtn?.addEventListener('click', () => {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    });

    // Handle home link click
    const homeLinks = document.querySelectorAll('.home-link');
    homeLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();

        // Clear hash from URL
        history.replaceState(null, document.title, window.location.pathname + window.location.search);

        // Scroll to top with smooth animation
        window.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      });
    });
  });
</script>

<style>
  .footer {
    background-color: #33272a;
    color: #f9fafb;
    padding: 5rem 0 2rem;
    position: relative;
  }

  .footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 3rem;
    margin-bottom: 3rem;
    align-items: start;
  }

  .footer-logo-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
    text-decoration: none;
  }

  .footer-logo-image {
    width: 60px;
    height: 60px;
    filter: brightness(1.1);
    transition: transform 0.3s ease;
  }

  .footer-logo-link:hover .footer-logo-image {
    transform: scale(1.05);
  }

  .footer-logo .logo-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
    display: inline-block;
  }

  .highlight {
    color: var(--primary-color);
  }

  .contact-info {
    margin-top: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .contact-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }

  .contact-item svg {
    color: var(--primary-color);
    flex-shrink: 0;
  }

  .contact-item a {
    color: #d1d5db;
    text-decoration: none;
    transition: color 0.3s ease;
  }

  .contact-item a:hover {
    color: var(--primary-color);
  }

  .social-icons {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
  }

  .social-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    color: #d1d5db;
    transition: all 0.3s ease;
  }

  .social-icon:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-3px);
  }

  .footer-links {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }

  .footer-column h3 {
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
    color: white;
    position: relative;
  }

  .footer-column h3::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 40px;
    height: 2px;
    background-color: var(--primary-color);
  }

  .footer-column ul {
    list-style: none;
    padding: 0;
  }

  .footer-column ul li {
    margin-bottom: 0.8rem;
  }

  .footer-column ul li a {
    color: #d1d5db;
    text-decoration: none;
    transition: color 0.3s ease;
  }

  .footer-column ul li a:hover {
    color: var(--primary-color);
  }

  .footer-bottom {
    border-top: 1px solid #374151;
    padding-top: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .copyright p {
    color: #9ca3af;
    margin: 0;
  }

  .footer-bottom-links {
    display: flex;
    gap: 1.5rem;
  }

  .footer-bottom-links a {
    color: #9ca3af;
    text-decoration: none;
    transition: color 0.3s ease;
    font-size: 0.9rem;
  }

  .footer-bottom-links a:hover {
    color: var(--primary-color);
  }

  #scrollToTop {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 999;
  }

  #scrollToTop.visible {
    opacity: 1;
    visibility: visible;
  }

  #scrollToTop:hover {
    background-color: var(--secondary-color);
    transform: translateY(-3px);
  }

  #scrollToTop svg {
    width: 24px;
    height: 24px;
  }

  @media (max-width: 768px) {
    .footer-content {
      grid-template-columns: 1fr;
      gap: 3rem;
    }

    .footer-links {
      grid-template-columns: 1fr 1fr;
    }

    .footer-bottom {
      flex-direction: column;
      gap: 1rem;
      text-align: center;
    }

    .footer-bottom-links {
      justify-content: center;
    }

    .footer-logo-link {
      justify-content: center;
    }

    .contact-info {
      align-items: center;
    }

    .social-icons {
      justify-content: center;
    }
  }

  @media (max-width: 480px) {
    .footer-links {
      grid-template-columns: 1fr;
    }

    .footer-column h3::after {
      left: 50%;
      transform: translateX(-50%);
    }

    .footer-column h3 {
      text-align: center;
    }

    .footer-column ul li {
      text-align: center;
    }
  }
</style>
