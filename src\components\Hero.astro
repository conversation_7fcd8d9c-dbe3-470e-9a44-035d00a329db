---
---

<section id="home" class="hero">
  <div class="particles-container">
    <div class="particle particle1"></div>
    <div class="particle particle2"></div>
    <div class="particle particle3"></div>
    <div class="particle particle4"></div>
    <div class="particle particle5"></div>
    <div class="particle particle6"></div>
    <div class="particle particle7"></div>
    <div class="particle particle8"></div>
    <!-- Enhanced decorative elements -->
    <div class="floating-orb orb1"></div>
    <div class="floating-orb orb2"></div>
    <div class="floating-orb orb3"></div>
    <div class="floating-orb orb4"></div>
    <div class="floating-orb orb5"></div>
    <!-- Geometric decorations -->
    <div class="geometric-shape triangle1"></div>
    <div class="geometric-shape triangle2"></div>
    <div class="geometric-shape hexagon1"></div>
    <div class="geometric-shape hexagon2"></div>
    <!-- Sparkle effects -->
    <div class="sparkle sparkle1"></div>
    <div class="sparkle sparkle2"></div>
    <div class="sparkle sparkle3"></div>
    <div class="sparkle sparkle4"></div>
    <div class="sparkle sparkle5"></div>
    <div class="sparkle sparkle6"></div>
  </div>

  <div class="container">
    <div class="hero-content">
      <div class="title-decoration">
        <div class="decoration-line"></div>
        <div class="decoration-circle"></div>
      </div>
      <div class="title-container">
        <h1 class="hero-title"><span class="orange-letter">J</span>upiter Tech</h1>
      </div>
      <h2 class="gradient-text">One Stop Service</h2>
      <div class="subtitle-decoration">
        <span class="dot"></span>
        <span class="line"></span>
        <span class="dot"></span>
      </div>
      <p class="hero-subtitle">
        บริการงานพิมพ์จดหมายในระบบดิจิตอล พร้อมจัดส่งไปรษณีย์ <span class="highlight">Total Printing</span>
        <br>
        บริการจัดพิมพ์ฉลากสินค้ากันปลอมแปลงในระบบ Laser, Inkjet ทั้งแบบม้วน แผ่น <span class="highlight">Advanced Label</span>
        <br>
        บริการด้านซอฟ์แวร์จัดการด้านสาธารณสุข <span class="highlight">Platform Tigra</span>
      </p>

      <style>
        .hero-subtitle {
          font-size: 1.2rem;
          line-height: 1.8;
          color: #5a4a4d; /* Darker text color for better contrast */
          margin: 1.5rem 0;
          max-width: 90%;
        }

        .hero-subtitle .highlight {
          color: var(--accent-color); /* Using accent color for more contrast */
          font-weight: 700; /* Bolder text */
          text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5); /* Text shadow for better readability */
        }

        @media (max-width: 768px) {
          .hero-subtitle {
        font-size: 1.1rem;
        line-height: 1.6;
        max-width: 100%;
        padding: 0 1rem;
          }
        }
      </style>
      <div class="hero-buttons">
        <a href="#contact" class="btn btn-primary">
          <span>ติดต่อเรา</span>
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="btn-icon">
            <path d="M5 12h14"></path>
            <path d="m12 5 7 7-7 7"></path>
          </svg>
        </a>
        <a href="#services" class="btn btn-outline">ผลิตภัณฑ์ของเรา</a>
      </div>
    </div>
    <div class="hero-image">
      <div class="image-wrapper">
        <div class="hero-image-container" id="draggableImage">
          <img src="/images/heropag/hero-img.png" alt="Jupiter Tech Hero Image" class="hero-img" draggable="false" />
        </div>
      </div>
    </div>
  </div>
</section>

<style>
  .hero {
    position: relative;
    padding: 10rem 0 8rem;
    background: linear-gradient(135deg, #ffe0c2 0%, #ffc080 100%);
    background-image:
      linear-gradient(135deg, #ffe0c2 0%, #ffc080 100%),
      repeating-linear-gradient(45deg, rgba(255, 106, 0, 0.05) 0px, rgba(255, 106, 0, 0.05) 1px, transparent 1px, transparent 10px),
      repeating-linear-gradient(135deg, rgba(255, 69, 0, 0.04) 0px, rgba(255, 69, 0, 0.04) 1px, transparent 1px, transparent 10px);
    overflow: hidden;
  }

  /* Particles Animation */
  .particles-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 0;
    pointer-events: none; /* Ensure particles don't interfere with clicks */
  }

  .particle {
    position: absolute;
    border-radius: 50%;
    opacity: 0.85; /* Increased opacity */
    pointer-events: none;
    z-index: -1; /* Ensure particles stay behind content */
    filter: blur(1px); /* Add slight blur for softer effect */
    box-shadow: 0 0 25px rgba(255, 106, 0, 0.3); /* Enhanced glow effect */
  }

  .particle1 {
    top: 10%;
    left: 20%;
    width: 110px; /* Larger size */
    height: 110px;
    background: radial-gradient(circle, rgba(255, 106, 0, 0.65) 0%, transparent 70%); /* More opaque */
    animation: float 15s ease-in-out infinite;
  }

  .particle2 {
    top: 20%;
    right: 15%;
    width: 90px; /* Larger size */
    height: 90px;
    background: radial-gradient(circle, rgba(255, 140, 0, 0.65) 0%, transparent 70%); /* More opaque */
    animation: float 12s ease-in-out infinite 2s;
  }

  .particle3 {
    bottom: 15%;
    left: 20%;
    width: 70px; /* Larger size */
    height: 70px;
    background: radial-gradient(circle, rgba(255, 69, 0, 0.65) 0%, transparent 70%); /* More opaque */
    animation: float 18s ease-in-out infinite 1s;
  }

  .particle4 {
    bottom: 30%;
    right: 25%;
    width: 80px; /* Larger size */
    height: 80px;
    background: radial-gradient(circle, rgba(255, 106, 0, 0.65) 0%, transparent 70%); /* More opaque */
    animation: float 20s ease-in-out infinite 3s;
  }

  .particle5 {
    top: 40%;
    left: 30%;
    width: 60px; /* Larger size */
    height: 60px;
    background: radial-gradient(circle, rgba(255, 140, 0, 0.65) 0%, transparent 70%); /* More opaque */
    animation: float 25s ease-in-out infinite;
  }

  .particle6 {
    top: 60%;
    right: 10%;
    width: 75px; /* Larger size */
    height: 75px;
    background: radial-gradient(circle, rgba(255, 69, 0, 0.65) 0%, transparent 70%); /* More opaque */
    animation: float 22s ease-in-out infinite 4s;
  }

  /* Add inner glow to particles */
  .particle::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40%;
    height: 40%;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    filter: blur(5px);
  }

  /* Two additional particles */
  .particle7 {
    top: 75%;
    left: 40%;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(255, 106, 0, 0.55) 0%, transparent 70%);
    animation: float 19s ease-in-out infinite 2s;
  }

  .particle8 {
    top: 25%;
    left: 60%;
    width: 85px;
    height: 85px;
    background: radial-gradient(circle, rgba(255, 69, 0, 0.55) 0%, transparent 70%);
    animation: float 17s ease-in-out infinite 3s;
  }

  @keyframes float {
    0% {
      transform: translate(0, 0) rotate(0deg);
    }
    25% {
      transform: translate(10px, 15px) rotate(5deg);
    }
    50% {
      transform: translate(5px, -10px) rotate(10deg);
    }
    75% {
      transform: translate(-10px, 5px) rotate(5deg);
    }
    100% {
      transform: translate(0, 0) rotate(0deg);
    }
  }

  @keyframes autoMove {
    0% {
      transform: translateY(-10px) translateX(0px) rotate(0deg);
    }
    25% {
      transform: translateY(-20px) translateX(15px) rotate(2deg);
    }
    50% {
      transform: translateY(-5px) translateX(-10px) rotate(-1deg);
    }
    75% {
      transform: translateY(-25px) translateX(8px) rotate(1deg);
    }
    100% {
      transform: translateY(-10px) translateX(0px) rotate(0deg);
    }
  }

  .container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    align-items: center;
    position: relative;
    z-index: 2; /* Increased z-index to ensure content is above particles */
  }

  .hero-content {
    position: relative;
    z-index: 3; /* Ensure content is above particles */
  }

  /* Title Decorations */
  .title-decoration {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    position: relative;
    z-index: 4;
  }

  .decoration-line {
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, #ff2d00, #ff4500, #ff6b1a, #ff8c42, #ffb347);
    border-radius: 3px;
    position: relative;
    overflow: hidden;
    box-shadow:
      0 0 15px rgba(255, 69, 0, 0.6),
      0 2px 8px rgba(255, 45, 0, 0.4);
  }

  .decoration-line::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
    animation: enhancedShimmer 1.5s infinite;
  }

  .decoration-line::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(90deg, rgba(255, 45, 0, 0.3), rgba(255, 107, 26, 0.3));
    border-radius: 5px;
    z-index: -1;
    filter: blur(4px);
  }

  .decoration-circle {
    width: 16px;
    height: 16px;
    background: radial-gradient(circle, #ff2d00 0%, #ff4500 50%, #ff6b1a 100%);
    border-radius: 50%;
    box-shadow:
      0 0 20px rgba(255, 45, 0, 0.8),
      0 0 40px rgba(255, 69, 0, 0.4),
      inset 0 0 8px rgba(255, 255, 255, 0.3);
    animation: enhancedPulse 2s infinite, circleGlow 3s ease-in-out infinite alternate;
    position: relative;
  }

  .decoration-circle::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 120%;
    height: 120%;
    background: radial-gradient(circle, rgba(255, 45, 0, 0.4) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    z-index: -1;
    animation: circleHalo 2s ease-in-out infinite alternate;
  }

  @keyframes enhancedShimmer {
    0% { left: -100%; }
    100% { left: 100%; }
  }

  @keyframes enhancedPulse {
    0%, 100% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.2);
      opacity: 0.8;
    }
  }

  @keyframes circleGlow {
    0% {
      box-shadow:
        0 0 20px rgba(255, 45, 0, 0.8),
        0 0 40px rgba(255, 69, 0, 0.4),
        inset 0 0 8px rgba(255, 255, 255, 0.3);
    }
    100% {
      box-shadow:
        0 0 30px rgba(255, 45, 0, 1),
        0 0 60px rgba(255, 69, 0, 0.6),
        inset 0 0 12px rgba(255, 255, 255, 0.5);
    }
  }

  @keyframes circleHalo {
    0% {
      opacity: 0.6;
      transform: translate(-50%, -50%) scale(1);
    }
    100% {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1.5);
    }
  }

  .title-container {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
  }

  .hero-title {
    font-size: 4rem;
    line-height: 1.1;
    font-weight: 800;
    margin: 0;
    background: linear-gradient(to right, #222 0%, #444 100%); /* Darker text gradient */
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.15); /* Enhanced shadow */
  }

  .orange-letter {
    color: var(--primary-color);
    background: none;
    -webkit-background-clip: initial;
    background-clip: initial;
    text-shadow: 0 0 10px rgba(255, 123, 0, 0.3), 2px 2px 6px rgba(255, 123, 0, 0.2);
    display: inline-block;
    transform: scale(1.05);
    animation: pulse 3s infinite ease-in-out;
    position: relative;
  }

  .orange-letter::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 120%;
    height: 120%;
    background: radial-gradient(circle, rgba(255, 123, 0, 0.15) 0%, transparent 70%);
    transform: translate(-50%, -50%);
    z-index: -1;
    border-radius: 50%;
    animation: glow 3s infinite ease-in-out alternate;
  }

  @keyframes glow {
    0% { opacity: 0.5; }
    100% { opacity: 1; }
  }

  @keyframes pulse {
    0%, 100% { transform: scale(1.05); }
    50% { transform: scale(1.15); }
  }

  .gradient-text {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0.5rem 0 1.5rem;
    color: #555555; /* Changed to medium gray color */
    display: inline-block;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.08); /* Subtle shadow for depth */
  }

  .subtitle-decoration {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
  }

  .dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: var(--primary-color);
  }

  .line {
    height: 2px;
    width: 40px;
    background-color: var(--border-color);
    margin: 0 8px;
  }

  .hero-subtitle {
    font-size: 1.2rem;
    color: #5a4a4d; /* Darker text color for better contrast */
    margin-bottom: 2rem;
    max-width: 90%;
    line-height: 1.6;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.3); /* Subtle text shadow for readability */
  }

  .hero-buttons {
    display: flex;
    gap: 1rem;
    position: relative;
    z-index: 5; /* Ensure button container is above particles */
  }

  .btn-primary {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 0.8rem 1.5rem;
    border-radius: 8px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(255, 123, 0, 0.3);
    border: none;
    position: relative; /* Add position relative */
    z-index: 5; /* Ensure button is above particles */
  }

  .btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(255, 123, 0, 0.4);
  }

  .btn-icon {
    width: 16px;
    height: 16px;
  }

  .btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    padding: 0.8rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative; /* Add position relative */
    z-index: 5; /* Ensure button is above particles */
  }

  .btn-outline:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  .hero-image {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .image-wrapper {
    position: relative;
    width: 100%;
    max-width: 700px;
    margin: 0 auto;
    z-index: 1;
  }

  .hero-image-container {
    position: relative;
    width: 100%;
    transition: all 0.5s ease;
    transform: translateY(-10px);
    z-index: 2;
    cursor: grab;
    user-select: none;
    animation: autoMove 8s ease-in-out infinite;
  }

  .hero-image-container:active {
    cursor: grabbing;
  }

  .hero-image-container.dragging {
    cursor: grabbing;
    z-index: 1000;
    animation: none; /* Disable auto animation while dragging */
  }

  .hero-image-container:hover {
    transform: translateY(-15px);
  }

  .hero-img {
    width: 100%;
    height: auto;
    display: block;
    object-fit: cover;
  }





  @media (max-width: 768px) {
    .hero {
      padding: 8rem 0 6rem;
    }

    .container {
      grid-template-columns: 1fr;
      text-align: center;
    }

    .hero-title {
      font-size: 3rem;
    }

    .orange-letter {
      transform: scale(1.03);
      text-shadow: 0 0 8px rgba(255, 123, 0, 0.3), 1px 1px 4px rgba(255, 123, 0, 0.2);
    }

    .orange-letter::after {
      width: 110%;
      height: 110%;
    }

    @keyframes pulse {
      0%, 100% { transform: scale(1.03); }
      50% { transform: scale(1.1); }
    }

    .gradient-text {
      font-size: 2rem;
      text-shadow: 0.5px 0.5px 1px rgba(0, 0, 0, 0.05);
      color: #666666; /* Slightly darker gray for better readability on mobile */
    }

    .hero-subtitle {
      max-width: 100%;
    }

    .hero-buttons {
      justify-content: center;
    }

    .hero-image {
      margin-top: 2rem;
      order: 2;
    }

    .hero-content {
      order: 1;
    }

    .title-decoration,
    .subtitle-decoration {
      justify-content: center;
    }

    .title-container {
      justify-content: center;
    }

    .image-wrapper {
      max-width: 90%;
      margin-top: 2rem;
    }

    .hero-image-container {
    }

    .hero-img {
    }
  }

  /* Enhanced Floating Orbs */
  .floating-orb {
    position: absolute;
    border-radius: 50%;
    pointer-events: none;
    z-index: 1;
    filter: blur(2px);
  }

  .orb1 {
    top: 8%;
    left: 12%;
    width: 25px;
    height: 25px;
    background: radial-gradient(circle, rgba(255, 215, 0, 0.9) 0%, rgba(255, 140, 0, 0.7) 50%, transparent 80%);
    animation: orbFloat 8s ease-in-out infinite, orbGlow 3s ease-in-out infinite alternate;
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
  }

  .orb2 {
    top: 25%;
    right: 8%;
    width: 18px;
    height: 18px;
    background: radial-gradient(circle, rgba(255, 99, 71, 0.9) 0%, rgba(255, 69, 0, 0.7) 50%, transparent 80%);
    animation: orbFloat 12s ease-in-out infinite 2s, orbGlow 4s ease-in-out infinite alternate;
    box-shadow: 0 0 15px rgba(255, 99, 71, 0.6);
  }

  .orb3 {
    bottom: 20%;
    left: 8%;
    width: 22px;
    height: 22px;
    background: radial-gradient(circle, rgba(255, 165, 0, 0.9) 0%, rgba(255, 140, 0, 0.7) 50%, transparent 80%);
    animation: orbFloat 10s ease-in-out infinite 1s, orbGlow 3.5s ease-in-out infinite alternate;
    box-shadow: 0 0 18px rgba(255, 165, 0, 0.6);
  }

  .orb4 {
    top: 45%;
    left: 5%;
    width: 15px;
    height: 15px;
    background: radial-gradient(circle, rgba(255, 140, 0, 0.9) 0%, rgba(255, 69, 0, 0.7) 50%, transparent 80%);
    animation: orbFloat 14s ease-in-out infinite 3s, orbGlow 2.5s ease-in-out infinite alternate;
    box-shadow: 0 0 12px rgba(255, 140, 0, 0.6);
  }

  .orb5 {
    bottom: 35%;
    right: 12%;
    width: 20px;
    height: 20px;
    background: radial-gradient(circle, rgba(255, 69, 0, 0.9) 0%, rgba(204, 55, 0, 0.7) 50%, transparent 80%);
    animation: orbFloat 16s ease-in-out infinite 4s, orbGlow 4.5s ease-in-out infinite alternate;
    box-shadow: 0 0 16px rgba(255, 69, 0, 0.6);
  }

  @keyframes orbFloat {
    0%, 100% {
      transform: translate(0, 0) scale(1);
      opacity: 0.8;
    }
    25% {
      transform: translate(15px, -10px) scale(1.1);
      opacity: 1;
    }
    50% {
      transform: translate(8px, -20px) scale(0.9);
      opacity: 0.9;
    }
    75% {
      transform: translate(-5px, -8px) scale(1.05);
      opacity: 1;
    }
  }

  @keyframes orbGlow {
    0% {
      filter: blur(2px);
      transform: scale(1);
    }
    100% {
      filter: blur(1px);
      transform: scale(1.2);
    }
  }

  /* Geometric Shapes */
  .geometric-shape {
    position: absolute;
    pointer-events: none;
    z-index: 1;
    filter: blur(1px);
  }

  .triangle1 {
    top: 15%;
    right: 20%;
    width: 0;
    height: 0;
    border-left: 15px solid transparent;
    border-right: 15px solid transparent;
    border-bottom: 25px solid rgba(255, 140, 0, 0.6);
    animation: triangleRotate 20s linear infinite, geometricFloat 8s ease-in-out infinite;
    filter: drop-shadow(0 0 10px rgba(255, 140, 0, 0.4));
  }

  .triangle2 {
    bottom: 25%;
    left: 15%;
    width: 0;
    height: 0;
    border-left: 12px solid transparent;
    border-right: 12px solid transparent;
    border-bottom: 20px solid rgba(255, 69, 0, 0.6);
    animation: triangleRotate 25s linear infinite reverse, geometricFloat 10s ease-in-out infinite 2s;
    filter: drop-shadow(0 0 8px rgba(255, 69, 0, 0.4));
  }

  .hexagon1 {
    top: 35%;
    right: 5%;
    width: 20px;
    height: 20px;
    background: rgba(255, 165, 0, 0.7);
    clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
    animation: hexagonSpin 15s linear infinite, geometricFloat 12s ease-in-out infinite 1s;
    filter: drop-shadow(0 0 12px rgba(255, 165, 0, 0.5));
  }

  .hexagon2 {
    bottom: 40%;
    right: 25%;
    width: 16px;
    height: 16px;
    background: rgba(255, 99, 71, 0.7);
    clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
    animation: hexagonSpin 18s linear infinite reverse, geometricFloat 9s ease-in-out infinite 3s;
    filter: drop-shadow(0 0 10px rgba(255, 99, 71, 0.5));
  }

  @keyframes triangleRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  @keyframes hexagonSpin {
    0% { transform: rotate(0deg) scale(1); }
    50% { transform: rotate(180deg) scale(1.1); }
    100% { transform: rotate(360deg) scale(1); }
  }

  @keyframes geometricFloat {
    0%, 100% {
      transform: translateY(0px);
      opacity: 0.7;
    }
    50% {
      transform: translateY(-15px);
      opacity: 1;
    }
  }

  /* Sparkle Effects */
  .sparkle {
    position: absolute;
    pointer-events: none;
    z-index: 2;
    width: 4px;
    height: 4px;
    background: radial-gradient(circle, rgba(255, 255, 255, 1) 0%, rgba(255, 215, 0, 0.8) 50%, transparent 80%);
    border-radius: 50%;
    animation: sparkleAnimation 3s ease-in-out infinite;
  }

  .sparkle1 {
    top: 12%;
    left: 25%;
    animation-delay: 0s;
  }

  .sparkle2 {
    top: 30%;
    right: 15%;
    animation-delay: 0.5s;
  }

  .sparkle3 {
    bottom: 30%;
    left: 20%;
    animation-delay: 1s;
  }

  .sparkle4 {
    top: 50%;
    left: 10%;
    animation-delay: 1.5s;
  }

  .sparkle5 {
    bottom: 15%;
    right: 30%;
    animation-delay: 2s;
  }

  .sparkle6 {
    top: 70%;
    right: 10%;
    animation-delay: 2.5s;
  }

  @keyframes sparkleAnimation {
    0%, 100% {
      opacity: 0;
      transform: scale(0.5);
    }
    50% {
      opacity: 1;
      transform: scale(1.5);
    }
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const draggableImage = document.getElementById('draggableImage');
    if (!draggableImage) return;

    let isDragging = false;
    let startX: number = 0;
    let startY: number = 0;
    let initialX: number = 0;
    let initialY: number = 0;

    function startDrag(e: MouseEvent | TouchEvent) {
      if (!draggableImage) return;
      isDragging = true;
      draggableImage.classList.add('dragging');

      // Get initial mouse/touch position
      const clientX = e.type === 'touchstart' ? (e as TouchEvent).touches[0].clientX : (e as MouseEvent).clientX;
      const clientY = e.type === 'touchstart' ? (e as TouchEvent).touches[0].clientY : (e as MouseEvent).clientY;

      startX = clientX;
      startY = clientY;

      // Get current transform values
      const transform = window.getComputedStyle(draggableImage).transform;
      if (transform !== 'none') {
        const matrix = new DOMMatrix(transform);
        initialX = matrix.e;
        initialY = matrix.f;
      } else {
        initialX = 0;
        initialY = 0;
      }

      // Prevent default behavior
      e.preventDefault();
    }

    function drag(e: MouseEvent | TouchEvent) {
      if (!isDragging || !draggableImage) return;

      e.preventDefault();

      // Get current mouse/touch position
      const clientX = e.type === 'touchmove' ? (e as TouchEvent).touches[0].clientX : (e as MouseEvent).clientX;
      const clientY = e.type === 'touchmove' ? (e as TouchEvent).touches[0].clientY : (e as MouseEvent).clientY;

      // Calculate new position
      const deltaX = clientX - startX;
      const deltaY = clientY - startY;

      const newX = initialX + deltaX;
      const newY = initialY + deltaY;

      // Apply transform
      draggableImage.style.transform = `translate(${newX}px, ${newY}px) translateY(-10px)`;
    }

    function endDrag() {
      if (!draggableImage) return;
      isDragging = false;
      draggableImage.classList.remove('dragging');

      // Re-enable auto animation after a short delay
      setTimeout(() => {
        if (draggableImage) {
          draggableImage.style.animation = 'autoMove 8s ease-in-out infinite';
        }
      }, 500);
    }

    // Mouse events
    draggableImage.addEventListener('mousedown', startDrag);
    document.addEventListener('mousemove', drag);
    document.addEventListener('mouseup', endDrag);

    // Touch events for mobile
    draggableImage.addEventListener('touchstart', startDrag);
    document.addEventListener('touchmove', drag);
    document.addEventListener('touchend', endDrag);
  });
</script>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    // Ensure the hero section is visible
    const heroSection = document.getElementById('home');

    // Function to ensure hero is visible
    function ensureHeroVisible() {
      if (heroSection) {
        // Get the hero section's position
        const heroRect = heroSection.getBoundingClientRect();

        // If hero is not visible at the top, scroll to it
        if (heroRect.top !== 0) {
          window.scrollTo({
            top: 0,
            behavior: 'auto'
          });
        }
      }
    }

    // Call immediately
    ensureHeroVisible();

    // And after a short delay to ensure everything is loaded
    setTimeout(ensureHeroVisible, 100);
    setTimeout(ensureHeroVisible, 500);

    // Also ensure hero is visible after window resize
    window.addEventListener('resize', ensureHeroVisible);
  });
</script>
