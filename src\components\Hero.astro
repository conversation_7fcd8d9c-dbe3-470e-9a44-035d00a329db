---
---

<section id="home" class="hero">
  <div class="particles-container">
    <!-- Premium floating elements -->
    <div class="floating-element circle1"></div>
    <div class="floating-element circle2"></div>
    <div class="floating-element circle3"></div>
    <div class="floating-element circle4"></div>
    <div class="floating-element circle5"></div>

    <!-- Geometric decorations -->
    <div class="geometric-shape triangle1"></div>
    <div class="geometric-shape triangle2"></div>
    <div class="geometric-shape hexagon1"></div>
    <div class="geometric-shape hexagon2"></div>

  </div>

  <div class="container">
    <div class="hero-content">
      <div class="title-decoration">
        <div class="decoration-line"></div>
      </div>
      <div class="title-container">
        <h1 class="hero-title"><span class="orange-letter">J</span>upiter Tech</h1>
      </div>
      <h2 class="gradient-text">One Stop Service</h2>
      <div class="subtitle-decoration">
        <span class="dot"></span>
        <span class="line"></span>
        <span class="dot"></span>
      </div>
      <p class="hero-subtitle">
        บริการงานพิมพ์จดหมายในระบบดิจิตอล พร้อมจัดส่งไปรษณีย์ <span class="highlight">Total Printing</span>
        <br>
        บริการจัดพิมพ์ฉลากสินค้ากันปลอมแปลงในระบบ Laser, Inkjet ทั้งแบบม้วน แผ่น <span class="highlight">Advanced Label</span>
        <br>
        บริการด้านซอฟ์แวร์จัดการด้านสาธารณสุข <span class="highlight">Platform Tigra</span>
      </p>

      <style>
        .hero-subtitle {
          font-size: 1.2rem;
          line-height: 1.6;
          color: #ffffff;
          margin: 1.5rem 0;
          max-width: 90%;
          text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.8);
          font-weight: 400;
          position: relative;
          z-index: 10;
        }

        .hero-subtitle .highlight {
          color: #ff9966;
          font-weight: 600;
          text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.8);
        }

        @media (max-width: 768px) {
          .hero-subtitle {
        font-size: 1.1rem;
        line-height: 1.6;
        max-width: 100%;
        padding: 0 1rem;
          }
        }
      </style>
      <div class="hero-buttons">
        <a href="#contact" class="btn btn-primary">
          <span>ติดต่อเรา</span>
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="btn-icon">
            <path d="M5 12h14"></path>
            <path d="m12 5 7 7-7 7"></path>
          </svg>
        </a>
        <a href="#services" class="btn btn-outline">ผลิตภัณฑ์ของเรา</a>
      </div>
    </div>
    <div class="hero-image">
      <div class="image-wrapper">
        <img src="/images/heropag/hero-img.png" alt="Jupiter Tech Hero Image" class="hero-img" />
      </div>
    </div>
  </div>
</section>

<style>
  .hero {
    position: relative;
    padding: 10rem 0 8rem;
    background: linear-gradient(135deg, #ffcc99 0%, #ff9966 100%);
    background-image:
      linear-gradient(120deg, #ffcc99 0%, #ffb380 40%, #ff9966 70%, #ff8c42 100%),
      radial-gradient(circle at 80% 20%, rgba(255, 183, 94, 0.25) 0%, transparent 60%),
      radial-gradient(circle at 20% 80%, rgba(255, 106, 0, 0.12) 0%, transparent 70%),
      repeating-linear-gradient(45deg, rgba(255, 106, 0, 0.05) 0px, rgba(255, 106, 0, 0.05) 1px, transparent 1px, transparent 10px),
      repeating-linear-gradient(135deg, rgba(255, 69, 0, 0.04) 0px, rgba(255, 69, 0, 0.04) 1px, transparent 1px, transparent 10px);
    overflow: hidden;
  }

  /* Particles Animation */
  .particles-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 0;
    pointer-events: none; /* Ensure particles don't interfere with clicks */
  }









  @keyframes float {
    0% {
      transform: translate(0, 0) rotate(0deg);
    }
    25% {
      transform: translate(10px, 15px) rotate(5deg);
    }
    50% {
      transform: translate(5px, -10px) rotate(10deg);
    }
    75% {
      transform: translate(-10px, 5px) rotate(5deg);
    }
    100% {
      transform: translate(0, 0) rotate(0deg);
    }
  }

  @keyframes autoMove {
    0% {
      transform: translateY(-10px) translateX(0px) rotate(0deg);
    }
    25% {
      transform: translateY(-20px) translateX(15px) rotate(2deg);
    }
    50% {
      transform: translateY(-5px) translateX(-10px) rotate(-1deg);
    }
    75% {
      transform: translateY(-25px) translateX(8px) rotate(1deg);
    }
    100% {
      transform: translateY(-10px) translateX(0px) rotate(0deg);
    }
  }

  .container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    align-items: center;
    position: relative;
    z-index: 2; /* Increased z-index to ensure content is above particles */
  }

  .hero-content {
    position: relative;
    z-index: 3; /* Ensure content is above particles */
  }

  /* Title Decorations */
  .title-decoration {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    position: relative;
    z-index: 4;
  }

  .decoration-line {
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, #ff2d00, #ff4500, #ff6b1a, #ff8c42, #ff9966);
    border-radius: 3px;
    position: relative;
    overflow: hidden;
    box-shadow:
      0 0 15px rgba(255, 69, 0, 0.6),
      0 2px 8px rgba(255, 45, 0, 0.4);
  }

  .decoration-line::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
    animation: enhancedShimmer 1.5s infinite;
  }

  .decoration-line::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(90deg, rgba(255, 45, 0, 0.3), rgba(255, 107, 26, 0.3));
    border-radius: 5px;
    z-index: -1;
    filter: blur(4px);
  }



  @keyframes enhancedShimmer {
    0% { left: -100%; }
    100% { left: 100%; }
  }



  .title-container {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
  }

  .hero-title {
    font-size: 4rem;
    line-height: 1.2;
    font-weight: 700;
    margin: 0;
    color: #ffffff;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    position: relative;
    z-index: 10;
  }

  .orange-letter {
    color: #ff6600;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    display: inline-block;
    font-weight: 700;
  }

  .orange-letter::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 120%;
    height: 120%;
    background: radial-gradient(circle, rgba(255, 123, 0, 0.15) 0%, transparent 70%);
    transform: translate(-50%, -50%);
    z-index: -1;
    border-radius: 50%;
    animation: glow 3s infinite ease-in-out alternate;
  }

  @keyframes glow {
    0% { opacity: 0.5; }
    100% { opacity: 1; }
  }

  @keyframes pulse {
    0%, 100% { transform: scale(1.05); }
    50% { transform: scale(1.15); }
  }

  .gradient-text {
    font-size: 2.2rem;
    font-weight: 600;
    margin: 0.5rem 0 1.5rem;
    color: #ffffff;
    display: inline-block;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    position: relative;
    z-index: 10;
  }

  .subtitle-decoration {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
  }

  .dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: var(--primary-color);
  }

  .line {
    height: 2px;
    width: 40px;
    background-color: var(--border-color);
    margin: 0 8px;
  }

  .hero-subtitle {
    font-size: 1.2rem;
    color: #5a4a4d; /* Darker text color for better contrast */
    margin-bottom: 2rem;
    max-width: 90%;
    line-height: 1.6;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.3); /* Subtle text shadow for readability */
  }

  .hero-buttons {
    display: flex;
    gap: 1rem;
    position: relative;
    z-index: 5; /* Ensure button container is above particles */
  }

  .btn-primary {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 0.8rem 1.5rem;
    border-radius: 8px;
    background: #ff6600;
    color: #ffffff;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(255, 102, 0, 0.3);
    border: none;
    position: relative;
    z-index: 15;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  }

  .btn-primary:hover {
    transform: translateY(-2px);
    background: #e55a00;
    box-shadow: 0 6px 16px rgba(255, 102, 0, 0.4);
  }

  .btn-icon {
    width: 16px;
    height: 16px;
  }

  .btn-outline {
    background: transparent;
    color: #ffffff;
    border: 2px solid #ffffff;
    padding: 0.8rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    position: relative;
    z-index: 15;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  }

  .btn-outline:hover {
    background: #ffffff;
    color: #ff6600;
    transform: translateY(-2px);
    text-shadow: none;
  }

  .hero-image {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .image-wrapper {
    position: relative;
    width: 100%;
    max-width: 700px;
    margin: 0 auto;
    z-index: 1;
  }

  .hero-img {
    width: 100%;
    height: auto;
    display: block;
    border-radius: 20px;
    animation: premiumHeroFloat 8s ease-in-out infinite;
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    filter: drop-shadow(0 15px 35px rgba(0, 0, 0, 0.2)) drop-shadow(0 5px 15px rgba(255, 106, 0, 0.1));
  }

  .hero-img:hover {
    animation-play-state: paused;
    transform: translateY(-25px) translateX(5px) scale(1.03) rotate(1deg);
    filter: drop-shadow(0 25px 50px rgba(0, 0, 0, 0.3)) drop-shadow(0 10px 25px rgba(255, 106, 0, 0.2));
  }

  @media (max-width: 768px) {
    .hero {
      padding: 8rem 0 6rem;
    }

    .container {
      grid-template-columns: 1fr;
      text-align: center;
    }

    .hero-title {
      font-size: 3rem;
    }

    .orange-letter {
      transform: scale(1.03);
      text-shadow: 0 0 8px rgba(255, 123, 0, 0.3), 1px 1px 4px rgba(255, 123, 0, 0.2);
    }

    .orange-letter::after {
      width: 110%;
      height: 110%;
    }

    @keyframes pulse {
      0%, 100% { transform: scale(1.03); }
      50% { transform: scale(1.1); }
    }

    .gradient-text {
      font-size: 2rem;
      text-shadow: 0.5px 0.5px 1px rgba(0, 0, 0, 0.05);
      color: #666666; /* Slightly darker gray for better readability on mobile */
    }

    .hero-subtitle {
      max-width: 100%;
    }

    .hero-buttons {
      justify-content: center;
    }

    .hero-image {
      margin-top: 2rem;
      order: 2;
    }

    .hero-content {
      order: 1;
    }

    .title-decoration,
    .subtitle-decoration {
      justify-content: center;
    }

    .title-container {
      justify-content: center;
    }

    .image-wrapper {
      max-width: 90%;
      margin-top: 2rem;
    }

    .hero-image-container {
    }

    .hero-img {
    }
  }



  /* Premium Floating Elements */
  .floating-element {
    position: absolute;
    pointer-events: none;
    z-index: 1;
    opacity: 0.7;
  }

  .circle1 {
    top: 15%;
    left: 8%;
    width: 18px;
    height: 18px;
    background: radial-gradient(circle, rgba(255, 140, 0, 0.9) 0%, rgba(255, 106, 0, 0.5) 100%);
    border-radius: 50%;
    animation: premiumFloat1 10s ease-in-out infinite;
    filter: drop-shadow(0 0 25px rgba(255, 140, 0, 0.8));
  }

  .circle2 {
    top: 65%;
    left: 12%;
    width: 14px;
    height: 14px;
    background: radial-gradient(circle, rgba(255, 165, 0, 0.9) 0%, rgba(255, 140, 0, 0.6) 100%);
    border-radius: 50%;
    animation: premiumFloat2 12s ease-in-out infinite 2s;
    filter: drop-shadow(0 0 20px rgba(255, 165, 0, 0.7));
  }

  .circle3 {
    top: 35%;
    right: 10%;
    width: 16px;
    height: 16px;
    background: radial-gradient(circle, rgba(255, 99, 71, 0.9) 0%, rgba(255, 69, 0, 0.5) 100%);
    border-radius: 50%;
    animation: premiumFloat3 14s ease-in-out infinite 4s;
    filter: drop-shadow(0 0 22px rgba(255, 99, 71, 0.7));
  }

  .circle4 {
    bottom: 25%;
    right: 15%;
    width: 12px;
    height: 12px;
    background: radial-gradient(circle, rgba(255, 140, 0, 0.8) 0%, rgba(255, 106, 0, 0.4) 100%);
    border-radius: 50%;
    animation: premiumFloat1 16s ease-in-out infinite 6s;
    filter: drop-shadow(0 0 18px rgba(255, 140, 0, 0.6));
  }

  .circle5 {
    top: 80%;
    left: 20%;
    width: 10px;
    height: 10px;
    background: radial-gradient(circle, rgba(255, 87, 0, 0.9) 0%, rgba(255, 69, 0, 0.5) 100%);
    border-radius: 50%;
    animation: premiumFloat2 18s ease-in-out infinite 8s;
    filter: drop-shadow(0 0 16px rgba(255, 87, 0, 0.7));
  }

  /* Geometric Shapes */
  .geometric-shape {
    position: absolute;
    pointer-events: none;
    z-index: 1;
    filter: blur(1px);
  }

  .triangle1 {
    top: 15%;
    right: 20%;
    width: 0;
    height: 0;
    border-left: 15px solid transparent;
    border-right: 15px solid transparent;
    border-bottom: 25px solid rgba(255, 140, 0, 0.6);
    animation: triangleRotate 20s linear infinite, geometricFloat 8s ease-in-out infinite;
    filter: drop-shadow(0 0 10px rgba(255, 140, 0, 0.4));
  }

  .triangle2 {
    bottom: 25%;
    left: 15%;
    width: 0;
    height: 0;
    border-left: 12px solid transparent;
    border-right: 12px solid transparent;
    border-bottom: 20px solid rgba(255, 69, 0, 0.6);
    animation: triangleRotate 25s linear infinite reverse, geometricFloat 10s ease-in-out infinite 2s;
    filter: drop-shadow(0 0 8px rgba(255, 69, 0, 0.4));
  }

  .hexagon1 {
    top: 35%;
    right: 5%;
    width: 20px;
    height: 20px;
    background: rgba(255, 165, 0, 0.7);
    clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
    animation: hexagonSpin 15s linear infinite, geometricFloat 12s ease-in-out infinite 1s;
    filter: drop-shadow(0 0 12px rgba(255, 165, 0, 0.5));
  }

  .hexagon2 {
    bottom: 40%;
    right: 25%;
    width: 16px;
    height: 16px;
    background: rgba(255, 99, 71, 0.7);
    clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
    animation: hexagonSpin 18s linear infinite reverse, geometricFloat 9s ease-in-out infinite 3s;
    filter: drop-shadow(0 0 10px rgba(255, 99, 71, 0.5));
  }

  @keyframes triangleRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  @keyframes hexagonSpin {
    0% { transform: rotate(0deg) scale(1); }
    50% { transform: rotate(180deg) scale(1.1); }
    100% { transform: rotate(360deg) scale(1); }
  }

  @keyframes geometricFloat {
    0%, 100% {
      transform: translateY(0px);
      opacity: 0.7;
    }
    50% {
      transform: translateY(-15px);
      opacity: 1;
    }
  }

  @keyframes premiumHeroFloat {
    0% {
      transform: translateY(-10px) translateX(0px) rotate(0deg) scale(1);
    }
    15% {
      transform: translateY(-20px) translateX(8px) rotate(0.5deg) scale(1.01);
    }
    30% {
      transform: translateY(-5px) translateX(12px) rotate(1deg) scale(1.02);
    }
    45% {
      transform: translateY(-25px) translateX(5px) rotate(0.5deg) scale(1.01);
    }
    60% {
      transform: translateY(-8px) translateX(-8px) rotate(-0.5deg) scale(1.02);
    }
    75% {
      transform: translateY(-18px) translateX(-12px) rotate(-1deg) scale(1.01);
    }
    90% {
      transform: translateY(-15px) translateX(-5px) rotate(-0.5deg) scale(1.01);
    }
    100% {
      transform: translateY(-10px) translateX(0px) rotate(0deg) scale(1);
    }
  }


</style>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const draggableImage = document.getElementById('draggableImage');
    if (!draggableImage) return;

    let isDragging = false;
    let startX: number = 0;
    let startY: number = 0;
    let initialX: number = 0;
    let initialY: number = 0;

    function startDrag(e: MouseEvent | TouchEvent) {
      if (!draggableImage) return;
      isDragging = true;
      draggableImage.classList.add('dragging');

      // Get initial mouse/touch position
      const clientX = e.type === 'touchstart' ? (e as TouchEvent).touches[0].clientX : (e as MouseEvent).clientX;
      const clientY = e.type === 'touchstart' ? (e as TouchEvent).touches[0].clientY : (e as MouseEvent).clientY;

      startX = clientX;
      startY = clientY;

      // Get current transform values
      const transform = window.getComputedStyle(draggableImage).transform;
      if (transform !== 'none') {
        const matrix = new DOMMatrix(transform);
        initialX = matrix.e;
        initialY = matrix.f;
      } else {
        initialX = 0;
        initialY = 0;
      }

      // Prevent default behavior
      e.preventDefault();
    }

    function drag(e: MouseEvent | TouchEvent) {
      if (!isDragging || !draggableImage) return;

      e.preventDefault();

      // Get current mouse/touch position
      const clientX = e.type === 'touchmove' ? (e as TouchEvent).touches[0].clientX : (e as MouseEvent).clientX;
      const clientY = e.type === 'touchmove' ? (e as TouchEvent).touches[0].clientY : (e as MouseEvent).clientY;

      // Calculate new position
      const deltaX = clientX - startX;
      const deltaY = clientY - startY;

      const newX = initialX + deltaX;
      const newY = initialY + deltaY;

      // Apply transform
      draggableImage.style.transform = `translate(${newX}px, ${newY}px) translateY(-10px)`;
    }

    function endDrag() {
      if (!draggableImage) return;
      isDragging = false;
      draggableImage.classList.remove('dragging');

      // Re-enable auto animation after a short delay
      setTimeout(() => {
        if (draggableImage) {
          draggableImage.style.animation = 'autoMove 8s ease-in-out infinite';
        }
      }, 500);
    }

    // Mouse events
    draggableImage.addEventListener('mousedown', startDrag);
    document.addEventListener('mousemove', drag);
    document.addEventListener('mouseup', endDrag);

    // Touch events for mobile
    draggableImage.addEventListener('touchstart', startDrag);
    document.addEventListener('touchmove', drag);
    document.addEventListener('touchend', endDrag);
  });
</script>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    // Ensure the hero section is visible
    const heroSection = document.getElementById('home');

    // Function to ensure hero is visible
    function ensureHeroVisible() {
      if (heroSection) {
        // Get the hero section's position
        const heroRect = heroSection.getBoundingClientRect();

        // If hero is not visible at the top, scroll to it
        if (heroRect.top !== 0) {
          window.scrollTo({
            top: 0,
            behavior: 'auto'
          });
        }
      }
    }

    // Call immediately
    ensureHeroVisible();

    // And after a short delay to ensure everything is loaded
    setTimeout(ensureHeroVisible, 100);
    setTimeout(ensureHeroVisible, 500);

    // Also ensure hero is visible after window resize
    window.addEventListener('resize', ensureHeroVisible);
  });
</script>
