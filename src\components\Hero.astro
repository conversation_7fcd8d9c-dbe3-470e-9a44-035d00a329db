---
---

<section id="home" class="hero">
  <div class="particles-container">


    <!-- Geometric decorations -->
    <div class="geometric-shape triangle1"></div>
    <div class="geometric-shape triangle2"></div>
    <div class="geometric-shape hexagon1"></div>
    <div class="geometric-shape hexagon2"></div>

  </div>

  <div class="container">
    <div class="hero-content">
      <div class="title-decoration">
        <div class="decoration-line"></div>
      </div>
      <div class="title-container">
        <h1 class="hero-title"><span class="orange-letter">J</span>upiter Tech</h1>
      </div>
      <h2 class="gradient-text">One Stop Service</h2>
      <div class="subtitle-decoration">
        <span class="dot"></span>
        <span class="line"></span>
        <span class="dot"></span>
      </div>
      <p class="hero-subtitle">
        บริการงานพิมพ์จดหมายในระบบดิจิตอล พร้อมจัดส่งไปรษณีย์ <span class="highlight">Total Printing</span>
        <br>
        บริการจัดพิมพ์ฉลากสินค้ากันปลอมแปลงในระบบ Laser, Inkjet ทั้งแบบม้วน แผ่น <span class="highlight">Advanced Label</span>
        <br>
        บริการด้านซอฟ์แวร์จัดการด้านสาธารณสุข <span class="highlight">Platform Tigra</span>
      </p>

      <style>
        .hero-subtitle {
          font-size: 1.3rem;
          line-height: 1.8;
          color: #ffffff;
          margin: 1.5rem 0;
          max-width: 90%;
          text-shadow:
            1px 1px 0px #000000,
            2px 2px 0px #333333,
            3px 3px 5px rgba(0, 0, 0, 0.7);
          font-weight: 500;
          letter-spacing: 0.3px;
          position: relative;
          z-index: 10;
        }

        .hero-subtitle .highlight {
          color: #ffff00;
          font-weight: 800;
          text-shadow:
            1px 1px 0px #000000,
            2px 2px 0px #cc6600,
            3px 3px 5px rgba(0, 0, 0, 0.8),
            0 0 15px rgba(255, 255, 0, 0.6);
          background: linear-gradient(135deg, #ffff00, #ffd700);
          -webkit-background-clip: text;
          background-clip: text;
          color: transparent;
          text-shadow: none;
          filter: drop-shadow(1px 1px 2px rgba(0, 0, 0, 0.8)) drop-shadow(0 0 10px rgba(255, 255, 0, 0.5));
        }

        @media (max-width: 768px) {
          .hero-subtitle {
        font-size: 1.1rem;
        line-height: 1.6;
        max-width: 100%;
        padding: 0 1rem;
          }
        }
      </style>
      <div class="hero-buttons">
        <a href="#contact" class="btn btn-primary">
          <span>ติดต่อเรา</span>
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="btn-icon">
            <path d="M5 12h14"></path>
            <path d="m12 5 7 7-7 7"></path>
          </svg>
        </a>
        <a href="#services" class="btn btn-outline">ผลิตภัณฑ์ของเรา</a>
      </div>
    </div>
    <div class="hero-image">
      <div class="image-wrapper">
        <img src="/images/heropag/hero-img.png" alt="Jupiter Tech Hero Image" class="hero-img" />
      </div>
    </div>
  </div>
</section>

<style>
  .hero {
    position: relative;
    padding: 10rem 0 8rem;
    background: linear-gradient(135deg, #ffe0c2 0%, #ffc080 100%);
    background-image:
      linear-gradient(120deg, #fff6e6 0%, #ffe0c2 40%, #ffd1a3 70%, #ffb347 100%),
      radial-gradient(circle at 80% 20%, rgba(255, 183, 94, 0.25) 0%, transparent 60%),
      radial-gradient(circle at 20% 80%, rgba(255, 106, 0, 0.12) 0%, transparent 70%),
      repeating-linear-gradient(45deg, rgba(255, 106, 0, 0.05) 0px, rgba(255, 106, 0, 0.05) 1px, transparent 1px, transparent 10px),
      repeating-linear-gradient(135deg, rgba(255, 69, 0, 0.04) 0px, rgba(255, 69, 0, 0.04) 1px, transparent 1px, transparent 10px);
    overflow: hidden;
  }

  /* Particles Animation */
  .particles-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 0;
    pointer-events: none; /* Ensure particles don't interfere with clicks */
  }









  @keyframes float {
    0% {
      transform: translate(0, 0) rotate(0deg);
    }
    25% {
      transform: translate(10px, 15px) rotate(5deg);
    }
    50% {
      transform: translate(5px, -10px) rotate(10deg);
    }
    75% {
      transform: translate(-10px, 5px) rotate(5deg);
    }
    100% {
      transform: translate(0, 0) rotate(0deg);
    }
  }

  @keyframes autoMove {
    0% {
      transform: translateY(-10px) translateX(0px) rotate(0deg);
    }
    25% {
      transform: translateY(-20px) translateX(15px) rotate(2deg);
    }
    50% {
      transform: translateY(-5px) translateX(-10px) rotate(-1deg);
    }
    75% {
      transform: translateY(-25px) translateX(8px) rotate(1deg);
    }
    100% {
      transform: translateY(-10px) translateX(0px) rotate(0deg);
    }
  }

  .container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    align-items: center;
    position: relative;
    z-index: 2; /* Increased z-index to ensure content is above particles */
  }

  .hero-content {
    position: relative;
    z-index: 3; /* Ensure content is above particles */
  }

  /* Title Decorations */
  .title-decoration {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    position: relative;
    z-index: 4;
  }

  .decoration-line {
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, #ff2d00, #ff4500, #ff6b1a, #ff8c42, #ffb347);
    border-radius: 3px;
    position: relative;
    overflow: hidden;
    box-shadow:
      0 0 15px rgba(255, 69, 0, 0.6),
      0 2px 8px rgba(255, 45, 0, 0.4);
  }

  .decoration-line::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
    animation: enhancedShimmer 1.5s infinite;
  }

  .decoration-line::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(90deg, rgba(255, 45, 0, 0.3), rgba(255, 107, 26, 0.3));
    border-radius: 5px;
    z-index: -1;
    filter: blur(4px);
  }



  @keyframes enhancedShimmer {
    0% { left: -100%; }
    100% { left: 100%; }
  }



  .title-container {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
  }

  .hero-title {
    font-size: 4.5rem;
    line-height: 1.1;
    font-weight: 900;
    margin: 0;
    color: #ffffff;
    text-shadow:
      2px 2px 0px #000000,
      4px 4px 0px #333333,
      6px 6px 8px rgba(0, 0, 0, 0.8),
      0 0 20px rgba(255, 255, 255, 0.5),
      0 0 40px rgba(255, 215, 0, 0.3);
    filter: drop-shadow(3px 3px 6px rgba(0, 0, 0, 0.7));
    letter-spacing: 1px;
    position: relative;
    z-index: 10;
  }

  .orange-letter {
    color: #ff4500;
    background: none;
    -webkit-background-clip: initial;
    background-clip: initial;
    text-shadow:
      2px 2px 0px #000000,
      4px 4px 0px #cc3700,
      6px 6px 8px rgba(0, 0, 0, 0.8),
      0 0 25px rgba(255, 69, 0, 0.9),
      0 0 50px rgba(255, 140, 0, 0.6);
    display: inline-block;
    transform: scale(1.15);
    animation: orangeLetterPulse 2.5s infinite ease-in-out;
    position: relative;
    z-index: 10;
    font-weight: 900;
  }

  @keyframes orangeLetterPulse {
    0%, 100% {
      transform: scale(1.15);
      text-shadow:
        2px 2px 0px #000000,
        4px 4px 0px #cc3700,
        6px 6px 8px rgba(0, 0, 0, 0.8),
        0 0 25px rgba(255, 69, 0, 0.9),
        0 0 50px rgba(255, 140, 0, 0.6);
    }
    50% {
      transform: scale(1.25);
      text-shadow:
        3px 3px 0px #000000,
        6px 6px 0px #cc3700,
        9px 9px 12px rgba(0, 0, 0, 0.9),
        0 0 35px rgba(255, 69, 0, 1),
        0 0 70px rgba(255, 140, 0, 0.8);
    }
  }

  .orange-letter::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 120%;
    height: 120%;
    background: radial-gradient(circle, rgba(255, 123, 0, 0.15) 0%, transparent 70%);
    transform: translate(-50%, -50%);
    z-index: -1;
    border-radius: 50%;
    animation: glow 3s infinite ease-in-out alternate;
  }

  @keyframes glow {
    0% { opacity: 0.5; }
    100% { opacity: 1; }
  }

  @keyframes pulse {
    0%, 100% { transform: scale(1.05); }
    50% { transform: scale(1.15); }
  }

  .gradient-text {
    font-size: 2.8rem;
    font-weight: 800;
    margin: 0.5rem 0 1.5rem;
    color: #ffffff;
    display: inline-block;
    text-shadow:
      2px 2px 0px #000000,
      4px 4px 0px #444444,
      6px 6px 8px rgba(0, 0, 0, 0.8),
      0 0 20px rgba(255, 255, 255, 0.4),
      0 0 40px rgba(255, 215, 0, 0.3);
    filter: drop-shadow(3px 3px 6px rgba(0, 0, 0, 0.6));
    animation: gradientTextGlow 3s ease-in-out infinite alternate;
    letter-spacing: 0.5px;
    position: relative;
    z-index: 10;
  }

  @keyframes gradientTextGlow {
    0% {
      text-shadow:
        2px 2px 0px #000000,
        4px 4px 0px #444444,
        6px 6px 8px rgba(0, 0, 0, 0.8),
        0 0 20px rgba(255, 255, 255, 0.4),
        0 0 40px rgba(255, 215, 0, 0.3);
      transform: scale(1);
    }
    100% {
      text-shadow:
        3px 3px 0px #000000,
        6px 6px 0px #444444,
        9px 9px 12px rgba(0, 0, 0, 0.9),
        0 0 30px rgba(255, 255, 255, 0.6),
        0 0 60px rgba(255, 215, 0, 0.5);
      transform: scale(1.02);
    }
  }

  .subtitle-decoration {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
  }

  .dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: var(--primary-color);
  }

  .line {
    height: 2px;
    width: 40px;
    background-color: var(--border-color);
    margin: 0 8px;
  }

  .hero-subtitle {
    font-size: 1.2rem;
    color: #5a4a4d; /* Darker text color for better contrast */
    margin-bottom: 2rem;
    max-width: 90%;
    line-height: 1.6;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.3); /* Subtle text shadow for readability */
  }

  .hero-buttons {
    display: flex;
    gap: 1rem;
    position: relative;
    z-index: 5; /* Ensure button container is above particles */
  }

  .btn-primary {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 1rem 2rem;
    border-radius: 10px;
    background: linear-gradient(135deg, #ff4500, #ff6b1a, #ff8c42);
    color: #ffffff;
    font-weight: 700;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    box-shadow:
      0 6px 20px rgba(255, 69, 0, 0.6),
      0 0 30px rgba(255, 107, 26, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
    border: 2px solid #cc3700;
    position: relative;
    z-index: 15;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    letter-spacing: 0.5px;
  }

  .btn-primary:hover {
    transform: translateY(-4px) scale(1.05);
    background: linear-gradient(135deg, #cc3700, #ff4500, #ff6b1a);
    box-shadow:
      0 10px 30px rgba(255, 69, 0, 0.8),
      0 0 40px rgba(255, 107, 26, 0.6),
      inset 0 1px 0 rgba(255, 255, 255, 0.4);
    border: 2px solid #ff2d00;
  }

  .btn-icon {
    width: 16px;
    height: 16px;
  }

  .btn-outline {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    border: 3px solid #ffffff;
    padding: 1rem 2rem;
    border-radius: 10px;
    font-weight: 700;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    position: relative;
    z-index: 15;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    letter-spacing: 0.5px;
    box-shadow:
      0 4px 15px rgba(255, 255, 255, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
  }

  .btn-outline:hover {
    background: linear-gradient(135deg, #ffffff, #f0f0f0);
    color: #ff4500;
    transform: translateY(-4px) scale(1.05);
    border: 3px solid #ff4500;
    box-shadow:
      0 8px 25px rgba(255, 255, 255, 0.5),
      0 0 30px rgba(255, 69, 0, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
    text-shadow: none;
  }

  .hero-image {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .image-wrapper {
    position: relative;
    width: 100%;
    max-width: 700px;
    margin: 0 auto;
    z-index: 1;
  }

  .hero-img {
    width: 100%;
    height: auto;
    display: block;
    transform: translateY(-10px);
    transition: transform 0.5s ease;
  }

  .hero-img:hover {
    transform: translateY(-15px);
  }

  @media (max-width: 768px) {
    .hero {
      padding: 8rem 0 6rem;
    }

    .container {
      grid-template-columns: 1fr;
      text-align: center;
    }

    .hero-title {
      font-size: 3rem;
    }

    .orange-letter {
      transform: scale(1.03);
      text-shadow: 0 0 8px rgba(255, 123, 0, 0.3), 1px 1px 4px rgba(255, 123, 0, 0.2);
    }

    .orange-letter::after {
      width: 110%;
      height: 110%;
    }

    @keyframes pulse {
      0%, 100% { transform: scale(1.03); }
      50% { transform: scale(1.1); }
    }

    .gradient-text {
      font-size: 2rem;
      text-shadow: 0.5px 0.5px 1px rgba(0, 0, 0, 0.05);
      color: #666666; /* Slightly darker gray for better readability on mobile */
    }

    .hero-subtitle {
      max-width: 100%;
    }

    .hero-buttons {
      justify-content: center;
    }

    .hero-image {
      margin-top: 2rem;
      order: 2;
    }

    .hero-content {
      order: 1;
    }

    .title-decoration,
    .subtitle-decoration {
      justify-content: center;
    }

    .title-container {
      justify-content: center;
    }

    .image-wrapper {
      max-width: 90%;
      margin-top: 2rem;
    }

    .hero-image-container {
    }

    .hero-img {
    }
  }



  /* Geometric Shapes */
  .geometric-shape {
    position: absolute;
    pointer-events: none;
    z-index: 1;
    filter: blur(1px);
  }

  .triangle1 {
    top: 15%;
    right: 20%;
    width: 0;
    height: 0;
    border-left: 15px solid transparent;
    border-right: 15px solid transparent;
    border-bottom: 25px solid rgba(255, 140, 0, 0.6);
    animation: triangleRotate 20s linear infinite, geometricFloat 8s ease-in-out infinite;
    filter: drop-shadow(0 0 10px rgba(255, 140, 0, 0.4));
  }

  .triangle2 {
    bottom: 25%;
    left: 15%;
    width: 0;
    height: 0;
    border-left: 12px solid transparent;
    border-right: 12px solid transparent;
    border-bottom: 20px solid rgba(255, 69, 0, 0.6);
    animation: triangleRotate 25s linear infinite reverse, geometricFloat 10s ease-in-out infinite 2s;
    filter: drop-shadow(0 0 8px rgba(255, 69, 0, 0.4));
  }

  .hexagon1 {
    top: 35%;
    right: 5%;
    width: 20px;
    height: 20px;
    background: rgba(255, 165, 0, 0.7);
    clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
    animation: hexagonSpin 15s linear infinite, geometricFloat 12s ease-in-out infinite 1s;
    filter: drop-shadow(0 0 12px rgba(255, 165, 0, 0.5));
  }

  .hexagon2 {
    bottom: 40%;
    right: 25%;
    width: 16px;
    height: 16px;
    background: rgba(255, 99, 71, 0.7);
    clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
    animation: hexagonSpin 18s linear infinite reverse, geometricFloat 9s ease-in-out infinite 3s;
    filter: drop-shadow(0 0 10px rgba(255, 99, 71, 0.5));
  }

  @keyframes triangleRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  @keyframes hexagonSpin {
    0% { transform: rotate(0deg) scale(1); }
    50% { transform: rotate(180deg) scale(1.1); }
    100% { transform: rotate(360deg) scale(1); }
  }

  @keyframes geometricFloat {
    0%, 100% {
      transform: translateY(0px);
      opacity: 0.7;
    }
    50% {
      transform: translateY(-15px);
      opacity: 1;
    }
  }


</style>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const draggableImage = document.getElementById('draggableImage');
    if (!draggableImage) return;

    let isDragging = false;
    let startX: number = 0;
    let startY: number = 0;
    let initialX: number = 0;
    let initialY: number = 0;

    function startDrag(e: MouseEvent | TouchEvent) {
      if (!draggableImage) return;
      isDragging = true;
      draggableImage.classList.add('dragging');

      // Get initial mouse/touch position
      const clientX = e.type === 'touchstart' ? (e as TouchEvent).touches[0].clientX : (e as MouseEvent).clientX;
      const clientY = e.type === 'touchstart' ? (e as TouchEvent).touches[0].clientY : (e as MouseEvent).clientY;

      startX = clientX;
      startY = clientY;

      // Get current transform values
      const transform = window.getComputedStyle(draggableImage).transform;
      if (transform !== 'none') {
        const matrix = new DOMMatrix(transform);
        initialX = matrix.e;
        initialY = matrix.f;
      } else {
        initialX = 0;
        initialY = 0;
      }

      // Prevent default behavior
      e.preventDefault();
    }

    function drag(e: MouseEvent | TouchEvent) {
      if (!isDragging || !draggableImage) return;

      e.preventDefault();

      // Get current mouse/touch position
      const clientX = e.type === 'touchmove' ? (e as TouchEvent).touches[0].clientX : (e as MouseEvent).clientX;
      const clientY = e.type === 'touchmove' ? (e as TouchEvent).touches[0].clientY : (e as MouseEvent).clientY;

      // Calculate new position
      const deltaX = clientX - startX;
      const deltaY = clientY - startY;

      const newX = initialX + deltaX;
      const newY = initialY + deltaY;

      // Apply transform
      draggableImage.style.transform = `translate(${newX}px, ${newY}px) translateY(-10px)`;
    }

    function endDrag() {
      if (!draggableImage) return;
      isDragging = false;
      draggableImage.classList.remove('dragging');

      // Re-enable auto animation after a short delay
      setTimeout(() => {
        if (draggableImage) {
          draggableImage.style.animation = 'autoMove 8s ease-in-out infinite';
        }
      }, 500);
    }

    // Mouse events
    draggableImage.addEventListener('mousedown', startDrag);
    document.addEventListener('mousemove', drag);
    document.addEventListener('mouseup', endDrag);

    // Touch events for mobile
    draggableImage.addEventListener('touchstart', startDrag);
    document.addEventListener('touchmove', drag);
    document.addEventListener('touchend', endDrag);
  });
</script>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    // Ensure the hero section is visible
    const heroSection = document.getElementById('home');

    // Function to ensure hero is visible
    function ensureHeroVisible() {
      if (heroSection) {
        // Get the hero section's position
        const heroRect = heroSection.getBoundingClientRect();

        // If hero is not visible at the top, scroll to it
        if (heroRect.top !== 0) {
          window.scrollTo({
            top: 0,
            behavior: 'auto'
          });
        }
      }
    }

    // Call immediately
    ensureHeroVisible();

    // And after a short delay to ensure everything is loaded
    setTimeout(ensureHeroVisible, 100);
    setTimeout(ensureHeroVisible, 500);

    // Also ensure hero is visible after window resize
    window.addEventListener('resize', ensureHeroVisible);
  });
</script>
