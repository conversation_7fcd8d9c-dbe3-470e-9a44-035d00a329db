---
---

<section id="home" class="hero">
  <div class="particles-container">
    <div class="particle particle1"></div>
    <div class="particle particle2"></div>
    <div class="particle particle3"></div>
    <div class="particle particle4"></div>
    <div class="particle particle5"></div>
    <div class="particle particle6"></div>
    <div class="particle particle7"></div>
    <div class="particle particle8"></div>
  </div>

  <div class="container">
    <div class="hero-content">
      <div class="title-decoration">
        <div class="decoration-line"></div>
        <div class="decoration-circle"></div>
      </div>
      <div class="title-container">
        <h1 class="hero-title"><span class="orange-letter">J</span>upiter Tech</h1>
      </div>
      <h2 class="gradient-text">One Stop Service</h2>
      <div class="subtitle-decoration">
        <span class="dot"></span>
        <span class="line"></span>
        <span class="dot"></span>
      </div>
      <p class="hero-subtitle">
        บริการงานพิมพ์จดหมายในระบบดิจิตอล พร้อมจัดส่งไปรษณีย์ <span class="highlight">Total Printing</span>
        <br>
        บริการจัดพิมพ์ฉลากสินค้ากันปลอมแปลงในระบบ Laser, Inkjet ทั้งแบบม้วน แผ่น <span class="highlight">Advanced Label</span>
        <br>
        บริการด้านซอฟ์แวร์จัดการด้านสาธารณสุข <span class="highlight">Platform Tigra</span>
      </p>

      <style>
        .hero-subtitle {
          font-size: 1.2rem;
          line-height: 1.8;
          color: #5a4a4d; /* Darker text color for better contrast */
          margin: 1.5rem 0;
          max-width: 90%;
        }

        .hero-subtitle .highlight {
          color: var(--accent-color); /* Using accent color for more contrast */
          font-weight: 700; /* Bolder text */
          text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5); /* Text shadow for better readability */
        }

        @media (max-width: 768px) {
          .hero-subtitle {
        font-size: 1.1rem;
        line-height: 1.6;
        max-width: 100%;
        padding: 0 1rem;
          }
        }
      </style>
      <div class="hero-buttons">
        <a href="#contact" class="btn btn-primary">
          <span>ติดต่อเรา</span>
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="btn-icon">
            <path d="M5 12h14"></path>
            <path d="m12 5 7 7-7 7"></path>
          </svg>
        </a>
        <a href="#services" class="btn btn-outline">ผลิตภัณฑ์ของเรา</a>
      </div>
    </div>
    <div class="hero-image">
      <div class="image-wrapper">
        <div class="hero-image-container" id="draggableImage">
          <img src="/images/heropag/hero-img.png" alt="Jupiter Tech Hero Image" class="hero-img" draggable="false" />
        </div>
      </div>
    </div>
  </div>
</section>

<style>
  .hero {
    position: relative;
    padding: 10rem 0 8rem;
    background: linear-gradient(135deg, #ffe0c2 0%, #ffc080 100%);
    background-image:
      linear-gradient(135deg, #ffe0c2 0%, #ffc080 100%),
      repeating-linear-gradient(45deg, rgba(255, 106, 0, 0.05) 0px, rgba(255, 106, 0, 0.05) 1px, transparent 1px, transparent 10px),
      repeating-linear-gradient(135deg, rgba(255, 69, 0, 0.04) 0px, rgba(255, 69, 0, 0.04) 1px, transparent 1px, transparent 10px);
    overflow: hidden;
  }

  /* Particles Animation */
  .particles-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 0;
    pointer-events: none; /* Ensure particles don't interfere with clicks */
  }

  .particle {
    position: absolute;
    border-radius: 50%;
    opacity: 0.85; /* Increased opacity */
    pointer-events: none;
    z-index: -1; /* Ensure particles stay behind content */
    filter: blur(1px); /* Add slight blur for softer effect */
    box-shadow: 0 0 25px rgba(255, 106, 0, 0.3); /* Enhanced glow effect */
  }

  .particle1 {
    top: 10%;
    left: 20%;
    width: 110px; /* Larger size */
    height: 110px;
    background: radial-gradient(circle, rgba(255, 106, 0, 0.65) 0%, transparent 70%); /* More opaque */
    animation: float 15s ease-in-out infinite;
  }

  .particle2 {
    top: 20%;
    right: 15%;
    width: 90px; /* Larger size */
    height: 90px;
    background: radial-gradient(circle, rgba(255, 140, 0, 0.65) 0%, transparent 70%); /* More opaque */
    animation: float 12s ease-in-out infinite 2s;
  }

  .particle3 {
    bottom: 15%;
    left: 20%;
    width: 70px; /* Larger size */
    height: 70px;
    background: radial-gradient(circle, rgba(255, 69, 0, 0.65) 0%, transparent 70%); /* More opaque */
    animation: float 18s ease-in-out infinite 1s;
  }

  .particle4 {
    bottom: 30%;
    right: 25%;
    width: 80px; /* Larger size */
    height: 80px;
    background: radial-gradient(circle, rgba(255, 106, 0, 0.65) 0%, transparent 70%); /* More opaque */
    animation: float 20s ease-in-out infinite 3s;
  }

  .particle5 {
    top: 40%;
    left: 30%;
    width: 60px; /* Larger size */
    height: 60px;
    background: radial-gradient(circle, rgba(255, 140, 0, 0.65) 0%, transparent 70%); /* More opaque */
    animation: float 25s ease-in-out infinite;
  }

  .particle6 {
    top: 60%;
    right: 10%;
    width: 75px; /* Larger size */
    height: 75px;
    background: radial-gradient(circle, rgba(255, 69, 0, 0.65) 0%, transparent 70%); /* More opaque */
    animation: float 22s ease-in-out infinite 4s;
  }

  /* Add inner glow to particles */
  .particle::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40%;
    height: 40%;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    filter: blur(5px);
  }

  /* Two additional particles */
  .particle7 {
    top: 75%;
    left: 40%;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(255, 106, 0, 0.55) 0%, transparent 70%);
    animation: float 19s ease-in-out infinite 2s;
  }

  .particle8 {
    top: 25%;
    left: 60%;
    width: 85px;
    height: 85px;
    background: radial-gradient(circle, rgba(255, 69, 0, 0.55) 0%, transparent 70%);
    animation: float 17s ease-in-out infinite 3s;
  }

  @keyframes float {
    0% {
      transform: translate(0, 0) rotate(0deg);
    }
    25% {
      transform: translate(10px, 15px) rotate(5deg);
    }
    50% {
      transform: translate(5px, -10px) rotate(10deg);
    }
    75% {
      transform: translate(-10px, 5px) rotate(5deg);
    }
    100% {
      transform: translate(0, 0) rotate(0deg);
    }
  }

  .container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    align-items: center;
    position: relative;
    z-index: 2; /* Increased z-index to ensure content is above particles */
  }

  .hero-content {
    position: relative;
    z-index: 3; /* Ensure content is above particles */
  }

  /* Title Decorations */
  .title-decoration {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
  }

  .decoration-line {
    width: 40px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    margin-right: 10px;
  }

  .decoration-circle {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: var(--accent-color);
  }

  .title-container {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
  }

  .hero-title {
    font-size: 4rem;
    line-height: 1.1;
    font-weight: 800;
    margin: 0;
    background: linear-gradient(to right, #222 0%, #444 100%); /* Darker text gradient */
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.15); /* Enhanced shadow */
  }

  .orange-letter {
    color: var(--primary-color);
    background: none;
    -webkit-background-clip: initial;
    background-clip: initial;
    text-shadow: 0 0 10px rgba(255, 123, 0, 0.3), 2px 2px 6px rgba(255, 123, 0, 0.2);
    display: inline-block;
    transform: scale(1.05);
    animation: pulse 3s infinite ease-in-out;
    position: relative;
  }

  .orange-letter::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 120%;
    height: 120%;
    background: radial-gradient(circle, rgba(255, 123, 0, 0.15) 0%, transparent 70%);
    transform: translate(-50%, -50%);
    z-index: -1;
    border-radius: 50%;
    animation: glow 3s infinite ease-in-out alternate;
  }

  @keyframes glow {
    0% { opacity: 0.5; }
    100% { opacity: 1; }
  }

  @keyframes pulse {
    0%, 100% { transform: scale(1.05); }
    50% { transform: scale(1.15); }
  }

  .gradient-text {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0.5rem 0 1.5rem;
    color: #555555; /* Changed to medium gray color */
    display: inline-block;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.08); /* Subtle shadow for depth */
  }

  .subtitle-decoration {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
  }

  .dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: var(--primary-color);
  }

  .line {
    height: 2px;
    width: 40px;
    background-color: var(--border-color);
    margin: 0 8px;
  }

  .hero-subtitle {
    font-size: 1.2rem;
    color: #5a4a4d; /* Darker text color for better contrast */
    margin-bottom: 2rem;
    max-width: 90%;
    line-height: 1.6;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.3); /* Subtle text shadow for readability */
  }

  .hero-buttons {
    display: flex;
    gap: 1rem;
    position: relative;
    z-index: 5; /* Ensure button container is above particles */
  }

  .btn-primary {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 0.8rem 1.5rem;
    border-radius: 8px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(255, 123, 0, 0.3);
    border: none;
    position: relative; /* Add position relative */
    z-index: 5; /* Ensure button is above particles */
  }

  .btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(255, 123, 0, 0.4);
  }

  .btn-icon {
    width: 16px;
    height: 16px;
  }

  .btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    padding: 0.8rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative; /* Add position relative */
    z-index: 5; /* Ensure button is above particles */
  }

  .btn-outline:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  .hero-image {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .image-wrapper {
    position: relative;
    width: 100%;
    max-width: 700px;
    margin: 0 auto;
    z-index: 1;
  }

  .hero-image-container {
    position: relative;
    width: 100%;
    transition: all 0.5s ease;
    transform: translateY(-10px);
    z-index: 2;
    cursor: grab;
    user-select: none;
  }

  .hero-image-container:active {
    cursor: grabbing;
  }

  .hero-image-container.dragging {
    cursor: grabbing;
    z-index: 1000;
  }

  .hero-image-container:hover {
    transform: translateY(-15px);
  }

  .hero-img {
    width: 100%;
    height: auto;
    display: block;
    object-fit: cover;
  }





  @media (max-width: 768px) {
    .hero {
      padding: 8rem 0 6rem;
    }

    .container {
      grid-template-columns: 1fr;
      text-align: center;
    }

    .hero-title {
      font-size: 3rem;
    }

    .orange-letter {
      transform: scale(1.03);
      text-shadow: 0 0 8px rgba(255, 123, 0, 0.3), 1px 1px 4px rgba(255, 123, 0, 0.2);
    }

    .orange-letter::after {
      width: 110%;
      height: 110%;
    }

    @keyframes pulse {
      0%, 100% { transform: scale(1.03); }
      50% { transform: scale(1.1); }
    }

    .gradient-text {
      font-size: 2rem;
      text-shadow: 0.5px 0.5px 1px rgba(0, 0, 0, 0.05);
      color: #666666; /* Slightly darker gray for better readability on mobile */
    }

    .hero-subtitle {
      max-width: 100%;
    }

    .hero-buttons {
      justify-content: center;
    }

    .hero-image {
      margin-top: 2rem;
      order: 2;
    }

    .hero-content {
      order: 1;
    }

    .title-decoration,
    .subtitle-decoration {
      justify-content: center;
    }

    .title-container {
      justify-content: center;
    }

    .image-wrapper {
      max-width: 90%;
      margin-top: 2rem;
    }

    .hero-image-container {
    }

    .hero-img {
    }
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const draggableImage = document.getElementById('draggableImage');
    if (!draggableImage) return;

    let isDragging = false;
    let startX: number = 0;
    let startY: number = 0;
    let initialX: number = 0;
    let initialY: number = 0;

    function startDrag(e: MouseEvent | TouchEvent) {
      if (!draggableImage) return;
      isDragging = true;
      draggableImage.classList.add('dragging');

      // Get initial mouse/touch position
      const clientX = e.type === 'touchstart' ? (e as TouchEvent).touches[0].clientX : (e as MouseEvent).clientX;
      const clientY = e.type === 'touchstart' ? (e as TouchEvent).touches[0].clientY : (e as MouseEvent).clientY;

      startX = clientX;
      startY = clientY;

      // Get current transform values
      const transform = window.getComputedStyle(draggableImage).transform;
      if (transform !== 'none') {
        const matrix = new DOMMatrix(transform);
        initialX = matrix.e;
        initialY = matrix.f;
      } else {
        initialX = 0;
        initialY = 0;
      }

      // Prevent default behavior
      e.preventDefault();
    }

    function drag(e: MouseEvent | TouchEvent) {
      if (!isDragging || !draggableImage) return;

      e.preventDefault();

      // Get current mouse/touch position
      const clientX = e.type === 'touchmove' ? (e as TouchEvent).touches[0].clientX : (e as MouseEvent).clientX;
      const clientY = e.type === 'touchmove' ? (e as TouchEvent).touches[0].clientY : (e as MouseEvent).clientY;

      // Calculate new position
      const deltaX = clientX - startX;
      const deltaY = clientY - startY;

      const newX = initialX + deltaX;
      const newY = initialY + deltaY;

      // Apply transform
      draggableImage.style.transform = `translate(${newX}px, ${newY}px) translateY(-10px)`;
    }

    function endDrag() {
      if (!draggableImage) return;
      isDragging = false;
      draggableImage.classList.remove('dragging');
    }

    // Mouse events
    draggableImage.addEventListener('mousedown', startDrag);
    document.addEventListener('mousemove', drag);
    document.addEventListener('mouseup', endDrag);

    // Touch events for mobile
    draggableImage.addEventListener('touchstart', startDrag);
    document.addEventListener('touchmove', drag);
    document.addEventListener('touchend', endDrag);
  });
</script>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    // Ensure the hero section is visible
    const heroSection = document.getElementById('home');

    // Function to ensure hero is visible
    function ensureHeroVisible() {
      if (heroSection) {
        // Get the hero section's position
        const heroRect = heroSection.getBoundingClientRect();

        // If hero is not visible at the top, scroll to it
        if (heroRect.top !== 0) {
          window.scrollTo({
            top: 0,
            behavior: 'auto'
          });
        }
      }
    }

    // Call immediately
    ensureHeroVisible();

    // And after a short delay to ensure everything is loaded
    setTimeout(ensureHeroVisible, 100);
    setTimeout(ensureHeroVisible, 500);

    // Also ensure hero is visible after window resize
    window.addEventListener('resize', ensureHeroVisible);
  });
</script>
