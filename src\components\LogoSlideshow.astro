---
---

<section class="logo-slideshow-section">
  <!-- No top wave divider needed since it's right after the Hero section -->

  <div class="container">
    <h2 class="section-title">พันธมิตรของเรา</h2>
    <p class="section-subtitle">หน่วยงานที่ไว้วางใจในบริการของเรา</p>

    <div class="logo-slider">
      <div class="logo-track">
        <!-- Logo items will be duplicated in JavaScript for infinite scroll effect -->
        <div class="logo-item"><img src="/images/logos/logo1.png" alt="Partner Logo 1" /></div>
        <div class="logo-item"><img src="/images/logos/logo2.png" alt="Partner Logo 2" /></div>
        <div class="logo-item"><img src="/images/logos/logo3.png" alt="Partner Logo 3" /></div>
        <div class="logo-item"><img src="/images/logos/logo4.png" alt="Partner Logo 4" /></div>
        <div class="logo-item"><img src="/images/logos/logo5.png" alt="Partner Logo 5" /></div>
        <div class="logo-item"><img src="/images/logos/logo6.png" alt="Partner Logo 6" /></div>
        <div class="logo-item"><img src="/images/logos/logo7.png" alt="Partner Logo 7" /></div>
        <div class="logo-item"><img src="/images/logos/logo8.png" alt="Partner Logo 8" /></div>
        <div class="logo-item"><img src="/images/logos/logo9.png" alt="Partner Logo 9" /></div>
        <div class="logo-item"><img src="/images/logos/logo10.png" alt="Partner Logo 10" /></div>
        <div class="logo-item"><img src="/images/logos/logo11.png" alt="Partner Logo 11" /></div>
        <div class="logo-item"><img src="/images/logos/logo12.png" alt="Partner Logo 12" /></div>
        <div class="logo-item"><img src="/images/logos/logo13.png" alt="Partner Logo 13" /></div>
        <div class="logo-item"><img src="/images/logos/logo14.png" alt="Partner Logo 14" /></div>
        <div class="logo-item"><img src="/images/logos/logo15.png" alt="Partner Logo 15" /></div>
        <div class="logo-item"><img src="/images/logos/logo16.png" alt="Partner Logo 16" /></div>
        <div class="logo-item"><img src="/images/logos/logo17.png" alt="Partner Logo 17" /></div>
        <div class="logo-item"><img src="/images/logos/logo18.png" alt="Partner Logo 17" /></div>
      </div>
    </div>
  </div>

  <div class="wave-divider bottom">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none">
      <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z" class="shape-fill"></path>
    </svg>
  </div>
</section>

<style>
  .logo-slideshow-section {
    position: relative;
    background-color: var(--background-color);
    padding: 2rem 0 4rem;
    overflow: hidden;
  }

  .wave-divider {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100px;
    line-height: 0;
    z-index: 1;
  }

  .wave-divider.bottom {
    top: auto;
    bottom: 0;
    transform: rotate(180deg);
  }

  .wave-divider svg {
    position: relative;
    display: block;
    width: calc(100% + 1.3px);
    height: 80px;
    filter: drop-shadow(0px -2px 2px rgba(0, 0, 0, 0.05));
  }

  .wave-divider .shape-fill {
    fill: var(--light-background);
  }

  .container {
    position: relative;
    z-index: 2;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-color);
  }

  .section-subtitle {
    text-align: center;
    font-size: 1.1rem;
    color: var(--text-secondary);
    max-width: 700px;
    margin: 0 auto 3rem;
  }

  .logo-slider {
    position: relative;
    width: 100%;
    overflow: hidden;
    padding: 20px 0;
  }

  .logo-track {
    display: flex;
    animation: scroll 40s linear infinite;
  }

  .logo-item {
    flex: 0 0 auto;
    width: 150px;
    height: 150px;
    margin: 0 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: white;
    border-radius: 50%;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;
  }

  .logo-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(255, 123, 0, 0.15);
  }

  .logo-item img {
    max-width: 90%;
    max-height: 90%;
    object-fit: contain;
  }

  @keyframes scroll {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(calc(-170px * 17)); /* Width of each logo item (150px) + margin (20px) * number of logos */
    }
  }

  @media (max-width: 768px) {
    .logo-item {
      width: 120px;
      height: 120px;
      margin: 0 15px;
    }

    @keyframes scroll {
      0% {
        transform: translateX(0);
      }
      100% {
        transform: translateX(calc(-135px * 17)); /* Width of each logo item (120px) + margin (15px) * number of logos */
      }
    }
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    // Clone the logo items to create a seamless infinite scroll effect
    const logoTrack = document.querySelector('.logo-track');
    if (logoTrack) {
      const logoItems = logoTrack.querySelectorAll('.logo-item');

      // Clone each logo item and append to the track
      logoItems.forEach(item => {
        const clone = item.cloneNode(true);
        logoTrack.appendChild(clone);
      });
    }
  });
</script>
