---
---

<header class="header">
  <div class="container">
    <div class="logo">
      <a href="/">
      <img src="/images/heropag/JT.png" alt="Jupiter Tech Logo" class="logo-image" width="50" height="50" />
      <span class="logo-text">
        <span class="animate-text" style="animation: slideIn 1s ease-out">Jupiter</span>
        <span class="highlight animate-text" style="animation: slideIn 1s ease-out 0.2s">Tech</span>
      </span>
      </a>
    </div>
    <nav class="nav">
      <ul class="nav-list">
        <li>
          <a href="#" class="nav-link home-link">
            <div class="icon-container">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="nav-icon">
                <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                <polyline points="9 22 9 12 15 12 15 22"></polyline>
              </svg>
            </div>
            <span>หน้าแรก</span>
          </a>
        </li>
        <li>
          <a href="#services" class="nav-link">
            <div class="icon-container">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="nav-icon">
                <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                <polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline>
                <line x1="12" y1="22.08" x2="12" y2="12"></line>
              </svg>
            </div>
            <span>ผลิตภัณฑ์ของเรา</span>
          </a>
        </li>
        <li>
          <a href="#contact" class="nav-link">
            <div class="icon-container">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="nav-icon">
                <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
              </svg>
            </div>
            <span>ติดต่อเรา</span>
          </a>
        </li>
        <li>
          <a href="#login-system" class="nav-link">
            <div class="icon-container">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="nav-icon">
                <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"></path>
                <polyline points="10 17 15 12 10 7"></polyline>
                <line x1="15" y1="12" x2="3" y2="12"></line>
              </svg>
            </div>
            <span>เข้าสู่ระบบ</span>
          </a>
        </li>
      </ul>
      <button class="mobile-menu-btn" aria-label="Toggle menu">
        <span class="bar"></span>
        <span class="bar"></span>
        <span class="bar"></span>
      </button>
    </nav>
  </div>
</header>

<script>
  // Mobile menu toggle
  const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
  const navList = document.querySelector('.nav-list');

  mobileMenuBtn?.addEventListener('click', () => {
    navList?.classList.toggle('active');
    mobileMenuBtn.classList.toggle('active');
  });

  // Close mobile menu when clicking on a link
  const navLinks = document.querySelectorAll('.nav-link');
  navLinks.forEach(link => {
    link.addEventListener('click', () => {
      navList?.classList.remove('active');
      mobileMenuBtn?.classList.remove('active');
    });
  });

  // Add animation to icons when page loads
  document.addEventListener('DOMContentLoaded', () => {
    const iconContainers = document.querySelectorAll('.icon-container');

    iconContainers.forEach((container, index) => {
      // Add staggered animation delay
      setTimeout(() => {
        container.classList.add('animate-in');
      }, 100 * index);
    });

    // Handle home link click to scroll to top
    const homeLink = document.querySelector('.home-link');
    if (homeLink) {
      homeLink.addEventListener('click', (e) => {
        e.preventDefault();

        // Clear hash from URL
        history.replaceState(null, document.title, window.location.pathname + window.location.search);

        // Scroll to top with smooth animation
        window.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      });
    }
  });
</script>

<style>
  .header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background-color: rgba(255, 255, 255, 0.95);
    box-shadow: 0 2px 10px rgba(255, 123, 0, 0.15);
    z-index: 1000;
    padding: 1rem 0;
    transition: all 0.3s ease;
    border-bottom: 2px solid var(--border-color);
  }

  .header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .logo {
    font-size: 1.5rem;
    font-weight: 700;
    display: flex;
    align-items: center;
  }

  .logo a {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
  }

  .logo-image {
    width: 50px;
    height: 50px;
    transition: transform 0.3s ease;
  }

  .logo:hover .logo-image {
    transform: scale(1.05);
  }

  .logo-text {
    color: var(--text-color);
  }

  .highlight {
    color: var(--primary-color);
  }

  .nav-list {
    display: flex;
    list-style: none;
    gap: 2rem;
  }

  .nav-link {
    color: var(--text-color);
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .icon-container {
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(255, 123, 0, 0.1), rgba(255, 158, 0, 0.2));
    box-shadow: 0 2px 5px rgba(255, 123, 0, 0.15);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    opacity: 0;
    transform: translateY(10px);
  }

  .icon-container.animate-in {
    animation: fadeInUp 0.5s forwards ease-out;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .icon-container::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .nav-icon {
    color: var(--primary-color);
    transition: all 0.3s ease;
    z-index: 1;
    filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.1));
  }

  .nav-link:hover {
    color: var(--primary-color);
  }

  .nav-link:hover .icon-container {
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 4px 8px rgba(255, 123, 0, 0.25);
    background: linear-gradient(135deg, rgba(255, 123, 0, 0.2), rgba(255, 158, 0, 0.3));
  }

  .nav-link:hover .icon-container::before {
    opacity: 1;
    animation: shine 1.5s ease-out;
  }

  @keyframes shine {
    0% {
      transform: rotate(0deg);
      opacity: 0;
    }
    25% {
      opacity: 0.3;
    }
    50% {
      opacity: 0.6;
    }
    100% {
      transform: rotate(360deg);
      opacity: 0;
    }
  }

  .nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: width 0.3s ease;
  }

  .nav-link:hover::after {
    width: 100%;
  }

  .mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
  }

  .bar {
    display: block;
    width: 25px;
    height: 3px;
    margin: 5px auto;
    background-color: var(--text-color);
    transition: all 0.3s ease;
  }

  @media (max-width: 768px) {
    .nav-list {
      position: fixed;
      top: 70px;
      left: -100%;
      flex-direction: column;
      background-color: white;
      width: 100%;
      text-align: center;
      transition: 0.3s;
      box-shadow: 0 10px 10px rgba(0, 0, 0, 0.1);
      padding: 2rem 0;
      gap: 1.5rem;
      z-index: 1000;
    }

    .nav-list.active {
      left: 0;
    }

    .nav-link {
      justify-content: center;
      padding: 0.8rem 1rem;
    }

    .icon-container {
      width: 32px;
      height: 32px;
      margin-right: 10px;
    }

    .nav-icon {
      width: 20px;
      height: 20px;
    }

    .mobile-menu-btn {
      display: block;
    }

    .mobile-menu-btn.active .bar:nth-child(2) {
      opacity: 0;
    }

    .mobile-menu-btn.active .bar:nth-child(1) {
      transform: translateY(8px) rotate(45deg);
    }

    .mobile-menu-btn.active .bar:nth-child(3) {
      transform: translateY(-8px) rotate(-45deg);
    }
  }
</style>
