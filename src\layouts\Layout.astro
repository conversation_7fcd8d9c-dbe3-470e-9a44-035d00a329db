
---
import PopupAd from '../components/PopupAd.astro';
---

<!doctype html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width" />
		<meta name="scroll-restoration" content="manual" />
		<link rel="icon" type="image/svg+xml" href="/public/images/heropag/JT.png" />
		<meta name="generator" content={Astro.generator} />
		<meta name="description" content="Jupiter Tech - บริการงานพิมพ์จดหมายในระบบดิจิตอล พร้อมจัดส่งไปรษณีย์ (Total Printing) บริการจัดพิมพ์ฉลากสินค้ากันปลอมแปลงในระบบ Laser, Inkjet ทั้งแบบม้วน แผ่น (Advanced Label) บริการด้านซอฟ์แวร์จัดการด้านสาธารณสุข (Platform Tigra)" />
		<meta name="keywords" content="Jupiter Tech, Total Printing, Advanced Label, Platform Tigra, U-Check, J-Track" />
		<link rel="preconnect" href="https://fonts.googleapis.com">
		<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
		<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
		<title>{Astro.props.title || 'Jupiter Tech - One Stop Service'}</title>
	</head>
	<body>
		<PopupAd />
		<slot />

		<script>
			// Disable browser's automatic scroll restoration
			if ('scrollRestoration' in history) {
				history.scrollRestoration = 'manual';
			}

			// Function to ensure we're at the top of the page
			function scrollToTop() {
				window.scrollTo({
					top: 0,
					left: 0,
					behavior: 'auto' // Use 'auto' for immediate scroll without animation
				});
			}

			// Scroll to top immediately when the script runs
			scrollToTop();

			// Also scroll to top when the page is fully loaded
			window.addEventListener('load', () => {
				// Clear any hash from the URL
				if (window.location.hash) {
					// Use history API to change the URL without reloading
					history.replaceState(null, document.title, window.location.pathname + window.location.search);
				}

				// Scroll to top
				scrollToTop();

				// Try again after a short delay (helps with some browsers)
				setTimeout(scrollToTop, 50);
			});

			// Before page unload, make sure we're at the top for the next load
			window.addEventListener('beforeunload', () => {
				scrollToTop();
			});

			// Additional check after DOM is loaded
			document.addEventListener('DOMContentLoaded', () => {
				scrollToTop();

				// Try again after a short delay
				setTimeout(scrollToTop, 100);
			});
		</script>
	</body>
</html>

<style is:global>
	:root {
		--primary-color: #ff6a00;
		--secondary-color: #ff8c00;
		--accent-color: #ff4500;
		--text-color: #222222;
		--light-text-color: #666666;
		--background-color: #ffffff;
		--light-background: #fff2e6;
		--border-color: #ffcca3;
	}

	* {
		box-sizing: border-box;
		margin: 0;
		padding: 0;
	}

	html,
	body {
		font-family: 'Inter', sans-serif;
		color: var(--text-color);
		background-color: var(--background-color);
		scroll-behavior: smooth;
		margin: 0;
		width: 100%;
		height: 100%;
		line-height: 1.6;
	}

	a {
		color: var(--primary-color);
		text-decoration: none;
		transition: color 0.3s ease;
	}

	a:hover {
		color: var(--secondary-color);
	}

	.container {
		width: 100%;
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 1.5rem;
	}

	.btn {
		display: inline-block;
		padding: 0.8rem 1.5rem;
		border-radius: 5px;
		font-weight: 500;
		cursor: pointer;
		transition: all 0.3s ease;
		border: none;
	}

	.btn-primary {
		background: var(--primary-color);
		color: white;
	}

	.btn-primary:hover {
		background: var(--secondary-color);
		color: white;
		transform: translateY(-2px);
		box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
	}

	.section {
		padding: 5rem 0;
	}

	.section-title {
		font-size: 2.5rem;
		margin-bottom: 1rem;
		text-align: center;
	}

	.section-subtitle {
		font-size: 1.2rem;
		color: #555555; /* Darker gray for better readability */
		margin-bottom: 3rem;
		text-align: center;
		max-width: 800px;
		margin-left: auto;
		margin-right: auto;
		line-height: 1.6;
		letter-spacing: 0.01em;
	}

	@media (max-width: 768px) {
		.section {
			padding: 3rem 0;
		}

		.section-title {
			font-size: 2rem;
		}

		.section-subtitle {
			font-size: 1.1rem;
			padding: 0 1rem;
		}
	}
</style>
