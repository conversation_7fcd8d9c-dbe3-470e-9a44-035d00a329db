---
---

<section id="testimonials" class="section testimonials">
  <div class="container">
    <h2 class="section-title">What Our Clients Say</h2>
    <p class="section-subtitle">Hear from businesses that have experienced our solutions</p>

    <div class="testimonial-slider">
      <div class="testimonial-track">
        <div class="testimonial-card">
          <div class="testimonial-content">
            <div class="quote-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                <path fill-rule="evenodd" d="M4.804 21.644A6.707 6.707 0 006 21.75a6.721 6.721 0 006.75-6.75c0-2.89-1.682-5.373-4.113-6.63a.75.75 0 01-.314-.974l1.45-3.365a.75.75 0 00-.88-1.001 42.355 42.355 0 00-7.877 4.58 .75.75 0 00-.248.813c.566 1.879 1.16 3.772 1.82 5.657a.75.75 0 01-.316.97l-2.156 1.19a.75.75 0 001.153.958l2.535-1.554zM12.75 21.75a6.75 6.75 0 100-13.5 6.75 6.75 0 000 13.5z" clip-rule="evenodd" />
                <path d="M15.75 7.5c-1.376 0-2.739.057-4.086.169C10.124 11.348 9 17.182 9 21.75h.75a.75.75 0 00.75-.75v-4.5a.75.75 0 01.75-.75h3a.75.75 0 01.75.75v4.5a.75.75 0 00.75.75h.75C16.5 17.182 15.376 11.348 13.836 7.669 12.489 7.557 11.126 7.5 9.75 7.5h6z" />
              </svg>
            </div>
            <p>"Jupiter Tech transformed our digital presence completely. Their team delivered a website that not only looks stunning but also performs exceptionally well. Our conversion rates have increased by 40% since launch!"</p>
            <div class="testimonial-author">
              <img src="https://randomuser.me/api/portraits/women/45.jpg" alt="Sarah Johnson" />
              <div>
                <h4>Sarah Johnson</h4>
                <p>CEO, TechStart Inc.</p>
              </div>
            </div>
          </div>
        </div>

        <div class="testimonial-card">
          <div class="testimonial-content">
            <div class="quote-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                <path fill-rule="evenodd" d="M4.804 21.644A6.707 6.707 0 006 21.75a6.721 6.721 0 006.75-6.75c0-2.89-1.682-5.373-4.113-6.63a.75.75 0 01-.314-.974l1.45-3.365a.75.75 0 00-.88-1.001 42.355 42.355 0 00-7.877 4.58 .75.75 0 00-.248.813c.566 1.879 1.16 3.772 1.82 5.657a.75.75 0 01-.316.97l-2.156 1.19a.75.75 0 001.153.958l2.535-1.554zM12.75 21.75a6.75 6.75 0 100-13.5 6.75 6.75 0 000 13.5z" clip-rule="evenodd" />
                <path d="M15.75 7.5c-1.376 0-2.739.057-4.086.169C10.124 11.348 9 17.182 9 21.75h.75a.75.75 0 00.75-.75v-4.5a.75.75 0 01.75-.75h3a.75.75 0 01.75.75v4.5a.75.75 0 00.75.75h.75C16.5 17.182 15.376 11.348 13.836 7.669 12.489 7.557 11.126 7.5 9.75 7.5h6z" />
              </svg>
            </div>
            <p>"The mobile app developed by Jupiter Tech has revolutionized how we interact with our customers. The intuitive design and seamless functionality have received overwhelmingly positive feedback. Highly recommended!"</p>
            <div class="testimonial-author">
              <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Michael Chen" />
              <div>
                <h4>Michael Chen</h4>
                <p>Marketing Director, Global Retail</p>
              </div>
            </div>
          </div>
        </div>

        <div class="testimonial-card">
          <div class="testimonial-content">
            <div class="quote-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                <path fill-rule="evenodd" d="M4.804 21.644A6.707 6.707 0 006 21.75a6.721 6.721 0 006.75-6.75c0-2.89-1.682-5.373-4.113-6.63a.75.75 0 01-.314-.974l1.45-3.365a.75.75 0 00-.88-1.001 42.355 42.355 0 00-7.877 4.58 .75.75 0 00-.248.813c.566 1.879 1.16 3.772 1.82 5.657a.75.75 0 01-.316.97l-2.156 1.19a.75.75 0 001.153.958l2.535-1.554zM12.75 21.75a6.75 6.75 0 100-13.5 6.75 6.75 0 000 13.5z" clip-rule="evenodd" />
                <path d="M15.75 7.5c-1.376 0-2.739.057-4.086.169C10.124 11.348 9 17.182 9 21.75h.75a.75.75 0 00.75-.75v-4.5a.75.75 0 01.75-.75h3a.75.75 0 01.75.75v4.5a.75.75 0 00.75.75h.75C16.5 17.182 15.376 11.348 13.836 7.669 12.489 7.557 11.126 7.5 9.75 7.5h6z" />
              </svg>
            </div>
            <p>"Working with Jupiter Tech on our digital marketing strategy has been a game-changer. Their data-driven approach and creative campaigns have significantly increased our online visibility and lead generation."</p>
            <div class="testimonial-author">
              <img src="https://randomuser.me/api/portraits/women/68.jpg" alt="Emily Rodriguez" />
              <div>
                <h4>Emily Rodriguez</h4>
                <p>Operations Manager, Innovate Solutions</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="testimonial-controls">
        <button class="control-btn prev" aria-label="Previous testimonial">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 19.5L8.25 12l7.5-7.5" />
          </svg>
        </button>
        <div class="testimonial-indicators">
          <button class="indicator active" aria-label="Go to testimonial 1"></button>
          <button class="indicator" aria-label="Go to testimonial 2"></button>
          <button class="indicator" aria-label="Go to testimonial 3"></button>
        </div>
        <button class="control-btn next" aria-label="Next testimonial">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
          </svg>
        </button>
      </div>
    </div>
  </div>
</section>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const track = document.querySelector('.testimonial-track');
    const cards = document.querySelectorAll('.testimonial-card');
    const prevBtn = document.querySelector('.control-btn.prev');
    const nextBtn = document.querySelector('.control-btn.next');
    const indicators = document.querySelectorAll('.indicator');

    let currentIndex = 0;

    function updateSlider() {
      if (!track) return;

      const cardWidth = cards[0].offsetWidth;
      track.style.transform = `translateX(-${currentIndex * cardWidth}px)`;

      // Update indicators
      indicators.forEach((indicator, index) => {
        indicator.classList.toggle('active', index === currentIndex);
      });
    }

    // Event listeners
    prevBtn?.addEventListener('click', () => {
      currentIndex = Math.max(0, currentIndex - 1);
      updateSlider();
    });

    nextBtn?.addEventListener('click', () => {
      currentIndex = Math.min(cards.length - 1, currentIndex + 1);
      updateSlider();
    });

    indicators.forEach((indicator, index) => {
      indicator.addEventListener('click', () => {
        currentIndex = index;
        updateSlider();
      });
    });

    // Handle window resize
    window.addEventListener('resize', updateSlider);

    // Initialize
    updateSlider();
  });
</script>

<style>
  .testimonials {
    background: linear-gradient(135deg, var(--light-background) 0%, #ffe0c2 100%);
  }

  .testimonial-slider {
    position: relative;
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
    overflow: hidden;
  }

  .testimonial-track {
    display: flex;
    transition: transform 0.5s ease;
  }

  .testimonial-card {
    min-width: 100%;
    padding: 1rem;
  }

  .testimonial-content {
    background-color: white;
    border-radius: 10px;
    padding: 2.5rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    position: relative;
  }

  .quote-icon {
    position: absolute;
    top: -15px;
    left: 30px;
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .quote-icon svg {
    width: 20px;
    height: 20px;
    color: white;
  }

  .testimonial-content > p {
    font-size: 1.1rem;
    line-height: 1.7;
    color: var(--text-color);
    margin-bottom: 1.5rem;
    font-style: italic;
  }

  .testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .testimonial-author img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--primary-color);
  }

  .testimonial-author h4 {
    font-size: 1.1rem;
    margin: 0;
    color: var(--text-color);
  }

  .testimonial-author p {
    margin: 0;
    color: var(--light-text-color);
    font-size: 0.9rem;
  }

  .testimonial-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 2rem;
    gap: 1rem;
  }

  .control-btn {
    background: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
  }

  .control-btn svg {
    width: 20px;
    height: 20px;
    color: var(--text-color);
  }

  .control-btn:hover {
    background: var(--primary-color);
  }

  .control-btn:hover svg {
    color: white;
  }

  .testimonial-indicators {
    display: flex;
    gap: 0.5rem;
  }

  .indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #d1d5db;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .indicator.active {
    background: var(--primary-color);
    transform: scale(1.2);
  }

  @media (max-width: 768px) {
    .testimonial-content {
      padding: 2rem 1.5rem;
    }
  }
</style>
