---
---

<section id="contact" class="section contact" style="padding: 4rem 0;">
  <!-- Background pattern -->
  <div class="bg-pattern">
    <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <pattern id="noise-pattern" width="100" height="100" patternUnits="userSpaceOnUse">
          <filter id="noise" x="0%" y="0%" width="100%" height="100%">
            <feTurbulence type="fractalNoise" baseFrequency="0.65" numOctaves="3" stitchTiles="stitch" />
            <feColorMatrix type="matrix" values="1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 0.1 0" />
          </filter>
          <rect width="100" height="100" fill="#fef6eb" filter="url(#noise)" opacity="0.03" />
        </pattern>
        <pattern id="dot-pattern" width="20" height="20" patternUnits="userSpaceOnUse">
          <circle cx="10" cy="10" r="1" fill="rgba(255, 123, 0, 0.05)" />
        </pattern>
      </defs>
      <rect width="100%" height="100%" fill="url(#noise-pattern)" />
      <rect width="100%" height="100%" fill="url(#dot-pattern)" />
    </svg>
  </div>

  <!-- Decorative elements -->
  <div class="contact-graphics">
    <div class="contact-graphic graphic1"></div>
    <div class="contact-graphic graphic2"></div>
    <div class="contact-graphic graphic3"></div>
  </div>

  <div class="container">
    <div class="modern-contact-header">
      <div class="header-background">
        <div class="floating-orb orb1"></div>
        <div class="floating-orb orb2"></div>
        <div class="floating-orb orb3"></div>
      </div>

      <div class="contact-badge">
        <div class="badge-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
          </svg>
        </div>
        <h2 class="modern-title">ติดต่อเรา</h2>
      </div>

      <div class="service-info-card">
        <div class="info-header">
          <div class="status-indicator">
            <div class="status-dot"></div>
            <span class="status-text">เปิดให้บริการ</span>
          </div>
        </div>

        <div class="time-display">
          <div class="time-icon-modern">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"/>
              <polyline points="12,6 12,12 16,14"/>
            </svg>
          </div>
          <div class="time-details">
            <div class="time-main">09:30 - 18:00 น.</div>
            <div class="time-sub">วันจันทร์ - เสาร์ (ยกเว้นวันหยุดนักขัตฤกษ์)</div>
          </div>
        </div>
      </div>
    </div>

    <div class="contact-container-full">
      <div class="contact-info">
        <div class="info-card">
          <div class="info-icon">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75" />
            </svg>
          </div>
          <div class="info-content">
            <h3>Email</h3>
            <p><EMAIL></p>
          </div>
        </div>

        <div class="info-card">
          <div class="info-icon">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 002.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 01-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 00-1.091-.852H4.5A2.25 2.25 0 002.25 4.5v2.25z" />
            </svg>
          </div>
          <div class="info-content">
            <h3>โทรศัพท์</h3>
            <p>091-794-1108</p>
          </div>
        </div>

        <div class="info-card">
          <div class="info-icon">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M15 10.5a3 3 0 11-6 0 3 3 0 016 0z" />
              <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1115 0z" />
            </svg>
          </div>
          <div class="info-content">
            <h3>ที่อยู่</h3>
            <p>บริษัท จูปิเตอร์ เทค จำกัด</p>
            <p>JUPITER TECH CO., LTD.</p>
          </div>
        </div>


      </div>
    </div>
  </div>
</section>

<style>
  .contact {
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #1a0f0a 0%, #2c1810 25%, #3d2317 50%, #4a2a1a 75%, #5c3520 100%);
    background-image:
      radial-gradient(circle at top right, rgba(255, 140, 0, 0.12) 0%, rgba(255, 106, 0, 0.08) 40%, transparent 70%),
      radial-gradient(circle at bottom left, rgba(255, 69, 0, 0.1) 0%, rgba(255, 87, 0, 0.06) 40%, transparent 70%),
      radial-gradient(circle at center, rgba(255, 255, 255, 0.02) 0%, transparent 60%),
      repeating-linear-gradient(45deg, rgba(255, 106, 0, 0.03) 0px, rgba(255, 106, 0, 0.03) 2px, transparent 2px, transparent 25px);
    box-shadow: inset 0 0 200px rgba(0, 0, 0, 0.4);
  }

  /* Background pattern styles */
  .bg-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.8;
    z-index: 0;
    pointer-events: none;
  }

  /* Decorative graphics */
  .contact-graphics {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    pointer-events: none;
  }

  .contact-graphic {
    position: absolute;
    border-radius: 50%;
  }

  .graphic1 {
    top: 5%;
    right: 5%;
    width: 250px;
    height: 250px;
    background: radial-gradient(circle, rgba(255, 140, 0, 0.15) 0%, rgba(255, 106, 0, 0.1) 40%, transparent 70%);
    animation: float-contact 15s ease-in-out infinite;
    filter: blur(1px);
  }

  .graphic2 {
    bottom: 10%;
    left: 5%;
    width: 180px;
    height: 180px;
    background: radial-gradient(circle, rgba(255, 69, 0, 0.12) 0%, rgba(255, 87, 0, 0.08) 40%, transparent 70%);
    animation: float-contact 18s ease-in-out infinite reverse;
    filter: blur(1px);
  }

  .graphic3 {
    top: 40%;
    left: 15%;
    width: 120px;
    height: 120px;
    background: radial-gradient(circle, rgba(255, 106, 0, 0.1) 0%, rgba(255, 140, 0, 0.06) 40%, transparent 70%);
    animation: float-contact 12s ease-in-out infinite;
    filter: blur(1px);
  }

  @keyframes float-contact {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    50% { transform: translate(20px, 20px) rotate(2deg); }
  }



  @keyframes float-card {
    0%, 100% {
      transform: perspective(1000px) rotateX(0deg) rotateY(0deg) translateY(0px);
    }
    50% {
      transform: perspective(1000px) rotateX(1deg) rotateY(1deg) translateY(-3px);
    }
  }

  .info-card {
    animation: float-card 8s ease-in-out infinite;
  }

  .info-card:nth-child(2) {
    animation-delay: 2.5s;
  }

  .info-card:nth-child(3) {
    animation-delay: 5s;
  }

  .contact-container-full {
    margin-top: 2rem;
    position: relative;
    z-index: 1;
  }

  .contact-info {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.2rem;
    max-width: 1000px;
    margin: 0 auto;
  }

  .container {
    position: relative;
    z-index: 1;
  }

  .modern-contact-header {
    position: relative;
    margin-bottom: 2.5rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.2rem;
  }

  .header-background {
    position: absolute;
    top: -50px;
    left: -50px;
    right: -50px;
    bottom: -50px;
    z-index: 0;
    overflow: hidden;
  }

  .floating-orb {
    position: absolute;
    border-radius: 50%;
    filter: blur(1px);
    animation: floatOrb 8s ease-in-out infinite;
  }

  .orb1 {
    top: 20%;
    left: 10%;
    width: 80px;
    height: 80px;
    background: radial-gradient(circle, rgba(255, 140, 0, 0.15) 0%, transparent 70%);
    animation-delay: 0s;
  }

  .orb2 {
    top: 60%;
    right: 15%;
    width: 60px;
    height: 60px;
    background: radial-gradient(circle, rgba(255, 106, 0, 0.12) 0%, transparent 70%);
    animation-delay: 2s;
  }

  .orb3 {
    bottom: 30%;
    left: 20%;
    width: 70px;
    height: 70px;
    background: radial-gradient(circle, rgba(255, 87, 0, 0.14) 0%, transparent 70%);
    animation-delay: 4s;
  }

  .contact-badge {
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.95) 0%,
      rgba(248, 250, 252, 0.9) 100%);
    backdrop-filter: blur(20px);
    border-radius: 40px;
    padding: 1rem 2rem;
    display: flex;
    align-items: center;
    gap: 0.8rem;
    box-shadow:
      0 15px 30px rgba(0, 0, 0, 0.08),
      0 8px 16px rgba(255, 106, 0, 0.04),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.3);
    position: relative;
    z-index: 2;
    animation: badgeFloat 6s ease-in-out infinite;
  }

  .badge-icon {
    width: 36px;
    height: 36px;
    background: linear-gradient(135deg, #ff6a00, #ff8c00);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    box-shadow:
      0 6px 12px rgba(255, 106, 0, 0.25),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
    animation: iconPulse 3s ease-in-out infinite;
  }

  .modern-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: #1a1a1a;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    letter-spacing: -0.5px;
  }

  .service-info-card {
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.98) 0%,
      rgba(248, 250, 252, 0.95) 100%);
    backdrop-filter: blur(25px);
    border-radius: 16px;
    padding: 1rem 1.5rem;
    max-width: 450px;
    width: 100%;
    box-shadow:
      0 12px 24px rgba(0, 0, 0, 0.04),
      0 6px 12px rgba(255, 106, 0, 0.02),
      inset 0 1px 0 rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.4);
    position: relative;
    z-index: 2;
    animation: cardFloat 8s ease-in-out infinite;
  }

  .info-header {
    display: flex;
    justify-content: center;
    margin-bottom: 1rem;
  }

  .status-indicator {
    display: flex;
    align-items: center;
    gap: 0.4rem;
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    padding: 0.4rem 0.8rem;
    border-radius: 16px;
    font-size: 0.85rem;
    font-weight: 600;
    box-shadow: 0 3px 8px rgba(16, 185, 129, 0.25);
  }

  .status-dot {
    width: 8px;
    height: 8px;
    background: #ffffff;
    border-radius: 50%;
    animation: statusPulse 2s ease-in-out infinite;
  }

  .time-display {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: linear-gradient(135deg,
      rgba(255, 106, 0, 0.05) 0%,
      rgba(255, 140, 0, 0.03) 100%);
    border-radius: 14px;
    padding: 1rem;
    border: 1px solid rgba(255, 106, 0, 0.1);
  }

  .time-icon-modern {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg,
      rgba(255, 106, 0, 0.1) 0%,
      rgba(255, 140, 0, 0.05) 100%);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ff6a00;
    font-size: 1.1rem;
    border: 1px solid rgba(255, 106, 0, 0.2);
    animation: timeIconFloat 4s ease-in-out infinite;
  }

  .time-details {
    flex: 1;
  }

  .time-main {
    font-size: 1.3rem;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 0.2rem;
    letter-spacing: -0.25px;
  }

  .time-sub {
    font-size: 0.9rem;
    color: #64748b;
    font-weight: 500;
    line-height: 1.4;
  }

  @keyframes floatOrb {
    0%, 100% {
      transform: translate(0, 0) scale(1);
    }
    50% {
      transform: translate(15px, -10px) scale(1.1);
    }
  }

  @keyframes badgeFloat {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-8px);
    }
  }

  @keyframes cardFloat {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-5px);
    }
  }

  @keyframes iconPulse {
    0%, 100% {
      transform: scale(1);
      box-shadow: 0 8px 16px rgba(255, 106, 0, 0.3);
    }
    50% {
      transform: scale(1.05);
      box-shadow: 0 12px 24px rgba(255, 106, 0, 0.4);
    }
  }

  @keyframes statusPulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.7;
    }
  }

  @keyframes timeIconFloat {
    0%, 100% {
      transform: translateY(0px) rotate(0deg);
    }
    50% {
      transform: translateY(-3px) rotate(2deg);
    }
  }

  .info-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(248, 248, 248, 0.9));
    border-radius: 20px;
    padding: 2rem;
    box-shadow:
      0 15px 35px rgba(0, 0, 0, 0.15),
      0 10px 25px rgba(255, 106, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.9),
      inset 0 -1px 0 rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    gap: 1.5rem;
    border: 1px solid rgba(255, 106, 0, 0.2);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    transform: perspective(1000px) rotateX(0deg);
    backdrop-filter: blur(10px);
  }

  .info-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 8px;
    height: 100%;
    background: linear-gradient(to bottom, var(--primary-color), var(--secondary-color));
    border-radius: 20px 0 0 20px;
    opacity: 0.7;
    transition: all 0.4s ease;
  }

  .info-card:hover {
    transform: perspective(1000px) rotateX(-3deg) translateY(-10px) translateZ(15px);
    box-shadow:
      0 25px 50px rgba(0, 0, 0, 0.2),
      0 15px 35px rgba(255, 106, 0, 0.15),
      inset 0 2px 0 rgba(255, 255, 255, 1);
  }

  .info-card:hover::before {
    width: 12px;
    opacity: 1;
  }

  .info-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(145deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    position: relative;
    box-shadow:
      0 10px 25px rgba(255, 106, 0, 0.3),
      0 5px 15px rgba(255, 106, 0, 0.2),
      inset 0 2px 0 rgba(255, 255, 255, 0.3),
      inset 0 -2px 0 rgba(0, 0, 0, 0.2);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    transform: translateZ(10px);
  }

  .info-icon::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    border-radius: 50%;
    background: linear-gradient(145deg, rgba(255, 106, 0, 0.3), rgba(255, 69, 0, 0.2));
    opacity: 0.6;
    z-index: -1;
    animation: pulse-glow 3s ease-in-out infinite;
  }

  .info-icon::after {
    content: '';
    position: absolute;
    top: 20%;
    left: 20%;
    width: 30%;
    height: 30%;
    background: rgba(255, 255, 255, 0.4);
    border-radius: 50%;
    filter: blur(3px);
  }

  .info-icon svg {
    width: 28px;
    height: 28px;
    color: white;
    transition: all 0.4s ease;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
    transform: translateZ(5px);
  }

  .info-card:hover .info-icon {
    transform: translateZ(20px) scale(1.1) rotateY(10deg);
    box-shadow:
      0 15px 35px rgba(255, 106, 0, 0.4),
      0 8px 20px rgba(255, 106, 0, 0.3),
      inset 0 3px 0 rgba(255, 255, 255, 0.4),
      inset 0 -3px 0 rgba(0, 0, 0, 0.3);
  }

  @keyframes pulse-glow {
    0%, 100% { opacity: 0.6; transform: scale(1); }
    50% { opacity: 0.8; transform: scale(1.05); }
  }



  .info-content h3 {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    color: #222222;
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    transform: translateZ(5px);
    transition: all 0.3s ease;
  }

  .info-content p {
    margin: 0;
    color: #555555;
    font-size: 0.9rem;
    font-weight: 500;
    line-height: 1.4;
    transform: translateZ(3px);
    transition: all 0.3s ease;
  }

  .info-card:hover .info-content h3 {
    color: #111111;
    transform: translateZ(10px);
  }

  .info-card:hover .info-content p {
    color: #333333;
    transform: translateZ(8px);
  }



  @media (max-width: 992px) {
    .contact-info {
      grid-template-columns: repeat(2, 1fr);
    }

    .modern-title {
      font-size: 2.2rem;
    }

    .contact-badge {
      padding: 0.8rem 1.8rem;
    }
  }

  @media (max-width: 768px) {
    .time-display {
      flex-direction: column;
      text-align: center;
      gap: 1rem;
    }

    .modern-title {
      font-size: 2rem;
    }

    .contact-badge {
      padding: 0.8rem 1.5rem;
      flex-direction: column;
      gap: 0.6rem;
    }

    .service-info-card {
      padding: 0.8rem 1.2rem;
    }
  }

  @media (max-width: 576px) {
    .contact-info {
      grid-template-columns: 1fr;
    }

    .info-card {
      padding: 1.2rem;
    }

    .modern-title {
      font-size: 1.8rem;
    }

    .contact-badge {
      padding: 0.8rem 1.2rem;
    }

    .service-info-card {
      padding: 0.7rem 1rem;
    }

    .time-main {
      font-size: 1.3rem;
    }

    .time-sub {
      font-size: 0.9rem;
    }

    .badge-icon {
      width: 40px;
      height: 40px;
    }

    .time-icon-modern {
      width: 40px;
      height: 40px;
    }
  }
</style>
