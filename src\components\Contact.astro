---
---

<section id="contact" class="section contact" style="padding: 4rem 0;">
  <!-- Background pattern -->
  <div class="bg-pattern">
    <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <pattern id="noise-pattern" width="100" height="100" patternUnits="userSpaceOnUse">
          <filter id="noise" x="0%" y="0%" width="100%" height="100%">
            <feTurbulence type="fractalNoise" baseFrequency="0.65" numOctaves="3" stitchTiles="stitch" />
            <feColorMatrix type="matrix" values="1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 0.1 0" />
          </filter>
          <rect width="100" height="100" fill="#fef6eb" filter="url(#noise)" opacity="0.03" />
        </pattern>
        <pattern id="dot-pattern" width="20" height="20" patternUnits="userSpaceOnUse">
          <circle cx="10" cy="10" r="1" fill="rgba(255, 123, 0, 0.05)" />
        </pattern>
      </defs>
      <rect width="100%" height="100%" fill="url(#noise-pattern)" />
      <rect width="100%" height="100%" fill="url(#dot-pattern)" />
    </svg>
  </div>

  <!-- Decorative elements -->
  <div class="contact-graphics">
    <div class="contact-graphic graphic1"></div>
    <div class="contact-graphic graphic2"></div>
    <div class="contact-graphic graphic3"></div>
  </div>

  <div class="container">
    <h2 class="section-title">ติดต่อเรา</h2>
    <p class="section-subtitle">Service Time (เวลาบริการ) 09:30 - 18:00 น. (วันจันทร์ - เสาร์) ยกเว้นวันหยุดนักขัตฤกษ์</p>

    <div class="contact-container-full">
      <div class="contact-info">
        <div class="info-card">
          <div class="info-icon">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75" />
            </svg>
          </div>
          <div class="info-content">
            <h3>Email</h3>
            <p><EMAIL></p>
          </div>
        </div>

        <div class="info-card">
          <div class="info-icon">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 002.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 01-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 00-1.091-.852H4.5A2.25 2.25 0 002.25 4.5v2.25z" />
            </svg>
          </div>
          <div class="info-content">
            <h3>โทรศัพท์</h3>
            <p>091-794-1108</p>
          </div>
        </div>

        <div class="info-card">
          <div class="info-icon">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M15 10.5a3 3 0 11-6 0 3 3 0 016 0z" />
              <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1115 0z" />
            </svg>
          </div>
          <div class="info-content">
            <h3>ที่อยู่</h3>
            <p>บริษัท จูปิเตอร์ เทค จำกัด</p>
            <p>JUPITER TECH CO., LTD.</p>
          </div>
        </div>


      </div>
    </div>
  </div>
</section>

<style>
  .contact {
    position: relative;
    overflow: hidden;
    background-color: #fef6eb;
    background-image:
      radial-gradient(circle at top right, rgba(255, 158, 0, 0.03), transparent 400px),
      radial-gradient(circle at bottom left, rgba(255, 123, 0, 0.03), transparent 400px),
      repeating-linear-gradient(45deg, rgba(255, 123, 0, 0.01) 0px, rgba(255, 123, 0, 0.01) 1px, transparent 1px, transparent 10px),
      repeating-linear-gradient(135deg, rgba(255, 84, 0, 0.01) 0px, rgba(255, 84, 0, 0.01) 1px, transparent 1px, transparent 10px);
  }

  /* Background pattern styles */
  .bg-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.8;
    z-index: 0;
    pointer-events: none;
  }

  /* Decorative graphics */
  .contact-graphics {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    pointer-events: none;
  }

  .contact-graphic {
    position: absolute;
    border-radius: 50%;
  }

  .graphic1 {
    top: 5%;
    right: 5%;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(255, 123, 0, 0.05) 0%, transparent 70%);
    animation: float-contact 15s ease-in-out infinite;
  }

  .graphic2 {
    bottom: 10%;
    left: 5%;
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, rgba(255, 158, 0, 0.04) 0%, transparent 70%);
    animation: float-contact 18s ease-in-out infinite reverse;
  }

  .graphic3 {
    top: 40%;
    left: 15%;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(255, 84, 0, 0.03) 0%, transparent 70%);
    animation: float-contact 12s ease-in-out infinite;
  }

  @keyframes float-contact {
    0%, 100% { transform: translate(0, 0); }
    50% { transform: translate(15px, 15px); }
  }

  .contact-container-full {
    margin-top: 2rem;
    position: relative;
    z-index: 1;
  }

  .contact-info {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.2rem;
    max-width: 1000px;
    margin: 0 auto;
  }

  .container {
    position: relative;
    z-index: 1;
  }

  .section-title {
    color: #33272a;
    margin-bottom: 0.5rem;
  }

  .section-subtitle {
    color: #7d6d70;
    font-size: 1.1rem;
    margin-bottom: 2rem;
  }

  .info-card {
    background-color: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 4px 12px rgba(255, 123, 0, 0.05);
    display: flex;
    align-items: center;
    gap: 1rem;
    border-right: 2px solid #ff9e00;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255, 158, 0, 0.03);
  }

  .info-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: #ff7b00;
    opacity: 0;
    transition: all 0.3s ease;
  }

  .info-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(255, 123, 0, 0.1);
  }

  .info-card:hover::before {
    opacity: 1;
  }

  .info-icon {
    width: 50px;
    height: 50px;
    background: #ff7b00;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    position: relative;
    box-shadow: 0 4px 10px rgba(255, 123, 0, 0.15);
    transition: all 0.3s ease;
  }

  .info-icon::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 50%;
    background: #ff7b00;
    opacity: 0.2;
    z-index: -1;
  }

  .info-icon::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40%;
    height: 40%;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    filter: blur(2px);
  }

  .info-icon svg {
    width: 22px;
    height: 22px;
    color: white;
    transition: all 0.3s ease;
  }

  .info-card:hover .info-icon {
    transform: scale(1.05);
    box-shadow: 0 6px 15px rgba(255, 123, 0, 0.2);
  }



  .info-content h3 {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    color: #33272a;
    font-weight: 600;
  }

  .info-content p {
    margin: 0;
    color: #7d6d70;
    font-size: 0.95rem;
  }



  @media (max-width: 992px) {
    .contact-info {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (max-width: 576px) {
    .contact-info {
      grid-template-columns: 1fr;
    }

    .info-card {
      padding: 1.2rem;
    }
  }
</style>
