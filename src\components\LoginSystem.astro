---
---

<div class="login-system-container">
  <div class="login-card">
    <div class="login-header">
      <div class="login-icon">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
          <circle cx="12" cy="7" r="4"></circle>
        </svg>
      </div>
      <h3>เข้าสู่ระบบของเรา</h3>
    </div>

    <div class="login-options">
      <a href="#" class="login-option-btn j-track">
      <div class="jtrack-logo shine-effect">
        <img src="/images/jtrack-logo.svg" alt="J-Track Logo" />
      </div>
      <span>J-Track</span>
      <div class="arrow-icon">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M5 12h14"></path>
        <path d="m12 5 7 7-7 7"></path>
        </svg>
      </div>
      </a>

      <a href="https://u-check.jupitertech.co.th/" class="login-option-btn u-check">
      <div class="ucheck-logo shine-effect">
        <img src="/images/ucheck-logo.svg" alt="U-Check Logo" />
      </div>
      <span>U-Check</span>
      <div class="arrow-icon">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M5 12h14"></path>
        <path d="m12 5 7 7-7 7"></path>
        </svg>
      </div>
      </a>
    </div>

    <style>
      .shine-effect {
      position: relative;
      overflow: hidden;
      }

      .shine-effect::after {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: linear-gradient(
        45deg,
        transparent 45%,
        rgba(255, 255, 255, 0.4) 50%,
        transparent 55%
      );
      transform: rotate(45deg);
      animation: shine 3s infinite;
      }

      @keyframes shine {
      0% { transform: translateX(-100%) rotate(45deg); }
      100% { transform: translateX(100%) rotate(45deg); }
      }

      .login-option-btn {
      backdrop-filter: blur(10px);
      background: rgba(255, 255, 255, 0.95);
      transform-origin: center;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      }

      .login-option-btn:hover {
      transform: scale(1.02) translateY(-2px);
      box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
      }
    </style>
  </div>
</div>

<style>
  .login-system-container {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
    padding: 1rem;
  }

  .login-card {
    background: rgba(255, 255, 255, 0.9); /* Slightly transparent white */
    border-radius: 16px;
    box-shadow: 0 15px 40px rgba(255, 123, 0, 0.25); /* More pronounced shadow */
    padding: 1.8rem; /* Slightly more padding */
    border-left: 5px solid var(--primary-color); /* Thicker border */
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(5px); /* Add blur effect */
  }

  .login-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 150px;
    height: 150px;
    background: linear-gradient(135deg, transparent 60%, rgba(255, 123, 0, 0.2));
    border-radius: 0 0 0 100%;
    z-index: -1;
    pointer-events: none;
  }

  /* Add another decorative element */
  .login-card::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 120px;
    height: 120px;
    background: linear-gradient(315deg, transparent 60%, rgba(255, 158, 0, 0.15));
    border-radius: 0 100% 0 0;
    z-index: -1;
    pointer-events: none;
  }

  .login-header {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    position: relative;
    z-index: 1;
  }

  .login-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    box-shadow: 0 5px 15px rgba(255, 123, 0, 0.2);
    position: relative; /* Added to ensure proper stacking context */
    z-index: 1; /* Ensure it's above the background elements but doesn't interfere with buttons */
  }

  .login-icon svg {
    width: 24px;
    height: 24px;
    color: white;
  }

  .login-header h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
  }

  .login-options {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    position: relative;
    z-index: 1;
  }

  .login-option-btn {
    display: flex;
    align-items: center;
    padding: 1.2rem 1.5rem; /* Slightly more padding */
    background-color: rgba(255, 255, 255, 0.85); /* Slightly transparent */
    border: 2px solid rgba(255, 123, 0, 0.2); /* Orange-tinted border */
    border-radius: 12px;
    transition: all 0.3s ease;
    text-decoration: none;
    color: var(--text-color);
    font-weight: 600; /* Slightly bolder text */
    position: relative;
    overflow: hidden;
    z-index: 2;
    box-shadow: 0 8px 20px rgba(255, 123, 0, 0.1); /* Add shadow */
  }

  .login-option-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    transition: all 0.3s ease;
  }

  .login-option-btn.j-track::before {
    background: linear-gradient(to bottom, var(--primary-color), var(--secondary-color));
  }

  .login-option-btn.u-check::before {
    background: linear-gradient(to bottom, var(--secondary-color), var(--accent-color));
  }

  .login-option-btn:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(255, 123, 0, 0.2);
    border-color: rgba(255, 123, 0, 0.5);
    background-color: rgba(255, 255, 255, 0.95);
  }

  .login-option-btn:hover::before {
    width: 10px;
  }

  .option-icon {
    width: 36px;
    height: 36px;
    background: #f8f8f8;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    transition: all 0.3s ease;
  }

  .ucheck-logo,
  .jtrack-logo {
    width: 48px;
    height: 48px;
    margin-right: 1rem;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    flex-shrink: 0;
  }

  .ucheck-logo img,
  .jtrack-logo img {
    width: 100%;
    height: 100%;
    display: block;
    object-fit: cover;
    border-radius: 8px;
  }

  .login-option-btn.j-track:hover .jtrack-logo,
  .login-option-btn.u-check:hover .ucheck-logo {
    transform: scale(1.1);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  }

  .option-icon svg {
    width: 20px;
    height: 20px;
    color: var(--text-color);
    transition: all 0.3s ease;
  }

  .login-option-btn.j-track:hover .option-icon svg {
    color: var(--primary-color);
  }

  .login-option-btn span {
    flex: 1;
    font-size: 1.1rem;
  }

  .arrow-icon {
    width: 24px;
    height: 24px;
    opacity: 0;
    transform: translateX(-10px);
    transition: all 0.3s ease;
  }

  .arrow-icon svg {
    width: 18px;
    height: 18px;
    color: var(--primary-color);
  }

  .login-option-btn:hover .arrow-icon {
    opacity: 1;
    transform: translateX(0);
  }

  @media (max-width: 768px) {
    .login-card {
      padding: 1.25rem;
    }

    .login-header h3 {
      font-size: 1.3rem;
    }

    .login-option-btn {
      padding: 0.8rem 1.2rem;
    }

    .option-icon {
      width: 32px;
      height: 32px;
    }

    .ucheck-logo,
    .jtrack-logo {
      width: 40px;
      height: 40px;
    }

    .login-option-btn span {
      font-size: 1rem;
    }
  }

  @media (max-width: 480px) {
    .login-header {
      flex-direction: column;
      align-items: flex-start;
    }

    .login-icon {
      margin-bottom: 0.5rem;
    }

    .ucheck-logo,
    .jtrack-logo {
      width: 36px;
      height: 36px;
    }
  }
</style>
