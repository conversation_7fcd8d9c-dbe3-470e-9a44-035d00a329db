---
---

<div class="login-system-container">
  <div class="login-card">
    <div class="login-header">
      <div class="login-icon">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
          <circle cx="12" cy="7" r="4"></circle>
        </svg>
      </div>
      <h3>เข้าสู่ระบบของเรา</h3>
    </div>

    <div class="login-options">
      <a href="#" class="login-option-btn j-track">
      <div class="jtrack-logo shine-effect">
        <img src="/images/jtrack-logo.svg" alt="J-Track Logo" />
      </div>
      <span>J-Track</span>
      <div class="arrow-icon">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M5 12h14"></path>
        <path d="m12 5 7 7-7 7"></path>
        </svg>
      </div>
      </a>

      <a href="https://u-check.jupitertech.co.th/" class="login-option-btn u-check">
      <div class="ucheck-logo shine-effect">
        <img src="/images/ucheck-logo.svg" alt="U-Check Logo" />
      </div>
      <span>U-Check</span>
      <div class="arrow-icon">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M5 12h14"></path>
        <path d="m12 5 7 7-7 7"></path>
        </svg>
      </div>
      </a>
    </div>

    <style>
      .shine-effect {
      position: relative;
      overflow: hidden;
      }

      .shine-effect::after {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: linear-gradient(
        45deg,
        transparent 45%,
        rgba(255, 255, 255, 0.4) 50%,
        transparent 55%
      );
      transform: rotate(45deg);
      animation: shine 3s infinite;
      }

      @keyframes shine {
      0% { transform: translateX(-100%) rotate(45deg); }
      100% { transform: translateX(100%) rotate(45deg); }
      }

      .login-option-btn {
      backdrop-filter: blur(10px);
      background: rgba(255, 255, 255, 0.95);
      transform-origin: center;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      }

      .login-option-btn:hover {
      transform: scale(1.02) translateY(-2px);
      box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
      }
    </style>
  </div>
</div>

<style>
  .login-system-container {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
    padding: 1rem;
  }

  .login-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(248, 248, 248, 0.9));
    border-radius: 20px;
    box-shadow:
      0 25px 50px rgba(255, 106, 0, 0.15),
      0 15px 35px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.9),
      inset 0 -1px 0 rgba(0, 0, 0, 0.05);
    padding: 2.5rem;
    border: 1px solid rgba(255, 106, 0, 0.2);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    transform: perspective(1000px) rotateX(0deg);
  }

  .login-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 106, 0, 0.1) 0%, transparent 70%);
    z-index: -1;
    pointer-events: none;
    animation: rotateGlow 20s linear infinite;
  }

  .login-card::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, transparent 50%, rgba(255, 106, 0, 0.15) 50%);
    border-radius: 0 20px 0 100px;
    z-index: -1;
    pointer-events: none;
  }

  @keyframes rotateGlow {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .login-header {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    position: relative;
    z-index: 1;
  }

  .login-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(145deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1.5rem;
    box-shadow:
      0 10px 25px rgba(255, 106, 0, 0.3),
      0 5px 15px rgba(255, 106, 0, 0.2),
      inset 0 2px 0 rgba(255, 255, 255, 0.3),
      inset 0 -2px 0 rgba(0, 0, 0, 0.2);
    position: relative;
    z-index: 1;
    transition: all 0.3s ease;
  }

  .login-icon:hover {
    transform: scale(1.1) rotateY(10deg);
    box-shadow:
      0 15px 35px rgba(255, 106, 0, 0.4),
      0 8px 20px rgba(255, 106, 0, 0.3),
      inset 0 3px 0 rgba(255, 255, 255, 0.4),
      inset 0 -3px 0 rgba(0, 0, 0, 0.3);
  }

  .login-icon svg {
    width: 28px;
    height: 28px;
    color: white;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
  }

  .login-header h3 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-color);
    margin: 0;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    background: linear-gradient(135deg, #333333, #555555);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
  }

  .login-options {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    position: relative;
    z-index: 1;
  }

  .login-option-btn {
    display: flex;
    align-items: center;
    padding: 1.5rem 2rem;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(248, 248, 248, 0.9));
    border: 2px solid rgba(255, 106, 0, 0.15);
    border-radius: 16px;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    text-decoration: none;
    color: var(--text-color);
    font-weight: 600;
    position: relative;
    overflow: hidden;
    z-index: 2;
    box-shadow:
      0 10px 25px rgba(255, 106, 0, 0.1),
      0 5px 15px rgba(0, 0, 0, 0.05),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
    transform: perspective(1000px) rotateX(0deg);
  }

  .login-option-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 6px;
    height: 100%;
    border-radius: 16px 0 0 16px;
    transition: all 0.4s ease;
    opacity: 0.8;
  }

  .login-option-btn.j-track::before {
    background: linear-gradient(to bottom, var(--primary-color), var(--secondary-color));
  }

  .login-option-btn.u-check::before {
    background: linear-gradient(to bottom, var(--secondary-color), var(--accent-color));
  }

  .login-option-btn:hover {
    transform: perspective(1000px) rotateX(-2deg) translateY(-8px) translateZ(10px);
    box-shadow:
      0 20px 40px rgba(255, 106, 0, 0.25),
      0 15px 30px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 1);
    border-color: rgba(255, 106, 0, 0.3);
    background: linear-gradient(145deg, rgba(255, 255, 255, 1), rgba(250, 250, 250, 0.95));
  }

  .login-option-btn:hover::before {
    width: 12px;
    opacity: 1;
  }

  .option-icon {
    width: 36px;
    height: 36px;
    background: #f8f8f8;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    transition: all 0.3s ease;
  }

  .ucheck-logo,
  .jtrack-logo {
    width: 56px;
    height: 56px;
    margin-right: 1.5rem;
    border-radius: 12px;
    overflow: hidden;
    box-shadow:
      0 6px 15px rgba(0, 0, 0, 0.15),
      0 3px 8px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    flex-shrink: 0;
    transform: translateZ(5px);
  }

  .ucheck-logo img,
  .jtrack-logo img {
    width: 100%;
    height: 100%;
    display: block;
    object-fit: cover;
    border-radius: 8px;
  }

  .login-option-btn.j-track:hover .jtrack-logo,
  .login-option-btn.u-check:hover .ucheck-logo {
    transform: translateZ(15px) scale(1.15) rotateY(5deg);
    box-shadow:
      0 10px 20px rgba(0, 0, 0, 0.2),
      0 5px 15px rgba(255, 106, 0, 0.15),
      inset 0 2px 0 rgba(255, 255, 255, 0.4);
  }

  .option-icon svg {
    width: 20px;
    height: 20px;
    color: var(--text-color);
    transition: all 0.3s ease;
  }

  .login-option-btn.j-track:hover .option-icon svg {
    color: var(--primary-color);
  }

  .login-option-btn span {
    flex: 1;
    font-size: 1.2rem;
    font-weight: 700;
    color: #333333;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    transform: translateZ(3px);
    transition: all 0.3s ease;
  }

  .login-option-btn:hover span {
    color: #222222;
    transform: translateZ(8px);
  }

  .arrow-icon {
    width: 32px;
    height: 32px;
    opacity: 0;
    transform: translateX(-15px) translateZ(0px);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    background: linear-gradient(135deg, rgba(255, 106, 0, 0.1), rgba(255, 69, 0, 0.05));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .arrow-icon svg {
    width: 20px;
    height: 20px;
    color: var(--primary-color);
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
  }

  .login-option-btn:hover .arrow-icon {
    opacity: 1;
    transform: translateX(0) translateZ(10px);
    background: linear-gradient(135deg, rgba(255, 106, 0, 0.2), rgba(255, 69, 0, 0.1));
    box-shadow: 0 4px 12px rgba(255, 106, 0, 0.3);
  }

  @media (max-width: 768px) {
    .login-card {
      padding: 1.25rem;
    }

    .login-header h3 {
      font-size: 1.3rem;
    }

    .login-option-btn {
      padding: 0.8rem 1.2rem;
    }

    .option-icon {
      width: 32px;
      height: 32px;
    }

    .ucheck-logo,
    .jtrack-logo {
      width: 40px;
      height: 40px;
    }

    .login-option-btn span {
      font-size: 1rem;
    }
  }

  @media (max-width: 480px) {
    .login-header {
      flex-direction: column;
      align-items: flex-start;
    }

    .login-icon {
      margin-bottom: 0.5rem;
    }

    .ucheck-logo,
    .jtrack-logo {
      width: 36px;
      height: 36px;
    }
  }
</style>
