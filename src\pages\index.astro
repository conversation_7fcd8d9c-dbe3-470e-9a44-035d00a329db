---
import Layout from '../layouts/Layout.astro';
import Header from '../components/Header.astro';
import Hero from '../components/Hero.astro';
// import Features from '../components/Features.astro';
import Services from '../components/Services.astro';
import Contact from '../components/Contact.astro';
import Footer from '../components/Footer.astro';
import LoginSystem from '../components/LoginSystem.astro';
import LogoSlideshow from '../components/LogoSlideshow.astro';
---

<Layout title="Jupiter Tech">
  <Header />
  <main>
    <Hero />
    <LogoSlideshow />

    <!-- <Features /> -->
    <Services />
    <section id="login-system" class="section login-section">
      <!-- Decorative graphics -->
      <div class="login-graphics">
        <div class="login-graphic shape1"></div>
        <div class="login-graphic shape2"></div>
        <div class="login-graphic shape3"></div>
        <div class="login-graphic shape4"></div>
        <div class="login-graphic dots">
          <svg width="150" height="150" viewBox="0 0 150 150" xmlns="http://www.w3.org/2000/svg">
            <pattern id="login-dots" x="0" y="0" width="15" height="15" patternUnits="userSpaceOnUse">
              <circle cx="2" cy="2" r="2" fill="rgba(255, 106, 0, 0.3)" />
            </pattern>
            <rect width="150" height="150" fill="url(#login-dots)" />
          </svg>
        </div>
        <div class="login-graphic lines">
          <svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
            <line x1="0" y1="40" x2="200" y2="40" stroke="rgba(255, 106, 0, 0.15)" stroke-width="1.5" />
            <line x1="0" y1="80" x2="200" y2="80" stroke="rgba(255, 106, 0, 0.15)" stroke-width="1.5" />
            <line x1="0" y1="120" x2="200" y2="120" stroke="rgba(255, 106, 0, 0.15)" stroke-width="1.5" />
            <line x1="0" y1="160" x2="200" y2="160" stroke="rgba(255, 106, 0, 0.15)" stroke-width="1.5" />
          </svg>
        </div>
      </div>

      <div class="container">
        <h2 class="section-title">เข้าสู่ระบบของเรา</h2>
        <p class="section-subtitle">เข้าถึงระบบจัดการงานและบริการต่างๆ ของเราได้อย่างสะดวกรวดเร็วผ่านระบบล็อกอินที่ปลอดภัย</p>
        <LoginSystem />
      </div>
    </section>
    <Contact />
  </main>
  <Footer />
</Layout>

<style>
  main {
    width: 100%;
  }

  .login-section {
    background-color: #ffd4a8; /* Darker background color */
    position: relative;
    overflow: hidden;
    background-image:
      radial-gradient(circle at 10% 20%, rgba(255, 106, 0, 0.15) 0%, transparent 40%),
      radial-gradient(circle at 90% 80%, rgba(255, 140, 0, 0.15) 0%, transparent 40%),
      linear-gradient(135deg, rgba(255, 106, 0, 0.12) 0%, rgba(255, 140, 0, 0.12) 100%);
  }

  /* Login graphics styles */
  .login-graphics {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 0;
  }

  .login-graphic {
    position: absolute;
    opacity: 0.85;
  }

  .shape1 {
    top: 15%;
    right: 10%;
    width: 90px;
    height: 90px;
    background: linear-gradient(135deg, rgba(255, 106, 0, 0.3), rgba(255, 140, 0, 0.3));
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    animation: morph 15s ease-in-out infinite;
    box-shadow: 0 0 20px rgba(255, 106, 0, 0.2);
  }

  .shape2 {
    bottom: 20%;
    left: 8%;
    width: 70px;
    height: 70px;
    background: linear-gradient(45deg, rgba(255, 140, 0, 0.3), rgba(255, 69, 0, 0.3));
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
    animation: morph 18s ease-in-out infinite alternate;
    box-shadow: 0 0 20px rgba(255, 140, 0, 0.2);
  }

  .shape3 {
    top: 60%;
    right: 15%;
    width: 50px;
    height: 50px;
    background: linear-gradient(to right, rgba(255, 106, 0, 0.25), rgba(255, 140, 0, 0.25));
    border-radius: 50%;
    animation: float 12s ease-in-out infinite;
    box-shadow: 0 0 15px rgba(255, 106, 0, 0.15);
  }

  .shape4 {
    top: 30%;
    left: 15%;
    width: 60px;
    height: 60px;
    background: linear-gradient(to bottom, rgba(255, 106, 0, 0.25), rgba(255, 140, 0, 0.25));
    transform: rotate(45deg);
    animation: rotate 20s linear infinite;
    box-shadow: 0 0 15px rgba(255, 106, 0, 0.15);
  }

  .dots {
    bottom: 10%;
    right: 20%;
    animation: float 15s ease-in-out infinite;
  }

  .lines {
    top: 5%;
    left: 5%;
    opacity: 0.5;
    animation: float 20s ease-in-out infinite reverse;
  }

  @keyframes morph {
    0%, 100% {
      border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    }
    25% {
      border-radius: 70% 30% 30% 70% / 70% 70% 30% 30%;
    }
    50% {
      border-radius: 30% 70% 70% 30% / 70% 30% 70% 30%;
    }
    75% {
      border-radius: 70% 30% 30% 70% / 30% 70% 30% 70%;
    }
  }

  @keyframes rotate {
    0% { transform: rotate(45deg); }
    100% { transform: rotate(405deg); }
  }

  .login-section::before {
    content: '';
    position: absolute;
    top: -50px;
    right: -50px;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(255, 106, 0, 0.25) 0%, transparent 70%);
    border-radius: 50%;
    z-index: -1;
    animation: float 15s ease-in-out infinite;
    pointer-events: none;
    filter: blur(2px);
    box-shadow: inset 0 0 30px rgba(255, 106, 0, 0.2);
  }

  .login-section::after {
    content: '';
    position: absolute;
    bottom: -50px;
    left: -50px;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(255, 140, 0, 0.25) 0%, transparent 70%);
    border-radius: 50%;
    z-index: -1;
    animation: float 18s ease-in-out infinite reverse;
    pointer-events: none;
    filter: blur(2px);
    box-shadow: inset 0 0 30px rgba(255, 140, 0, 0.2);
  }

  /* Add additional decorative elements */
  .login-section .container::before {
    content: '';
    position: absolute;
    top: 20%;
    left: 5%;
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, rgba(255, 69, 0, 0.2) 0%, transparent 70%);
    border-radius: 50%;
    z-index: -1;
    animation: float 12s ease-in-out infinite;
    pointer-events: none;
    filter: blur(1px);
    box-shadow: inset 0 0 20px rgba(255, 69, 0, 0.15);
  }

  .login-section .container::after {
    content: '';
    position: absolute;
    bottom: 15%;
    right: 8%;
    width: 180px;
    height: 180px;
    background: radial-gradient(circle, rgba(255, 106, 0, 0.2) 0%, transparent 70%);
    border-radius: 50%;
    z-index: -1;
    animation: float 20s ease-in-out infinite reverse;
    pointer-events: none;
    filter: blur(1px);
    box-shadow: inset 0 0 20px rgba(255, 106, 0, 0.15);
  }

  @keyframes float {
    0% {
      transform: translate(0, 0) scale(1);
      opacity: 0.8;
    }
    25% {
      transform: translate(8px, 12px) scale(1.03);
      opacity: 1;
    }
    50% {
      transform: translate(15px, 15px) scale(1.07);
      opacity: 0.9;
    }
    75% {
      transform: translate(8px, 3px) scale(1.05);
      opacity: 0.95;
    }
    100% {
      transform: translate(0, 0) scale(1);
      opacity: 0.8;
    }
  }
</style>