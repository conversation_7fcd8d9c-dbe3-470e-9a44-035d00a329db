---
import Layout from '../layouts/Layout.astro';
import Header from '../components/Header.astro';
import Hero from '../components/Hero.astro';
// import Features from '../components/Features.astro';
import Services from '../components/Services.astro';
import Contact from '../components/Contact.astro';
import Footer from '../components/Footer.astro';
import LoginSystem from '../components/LoginSystem.astro';
import LogoSlideshow from '../components/LogoSlideshow.astro';
---

<Layout title="Jupiter Tech">
  <Header />
  <main>
    <Hero />
    <LogoSlideshow />

    <!-- <Features /> -->
    <Services />
    <section id="login-system" class="section login-section">
      <!-- Decorative graphics -->
      <div class="login-graphics">
        <div class="login-graphic shape1"></div>
        <div class="login-graphic shape2"></div>
        <div class="login-graphic shape3"></div>
        <div class="login-graphic shape4"></div>
        <div class="login-graphic dots">
          <svg width="150" height="150" viewBox="0 0 150 150" xmlns="http://www.w3.org/2000/svg">
            <pattern id="login-dots" x="0" y="0" width="15" height="15" patternUnits="userSpaceOnUse">
              <circle cx="2" cy="2" r="2" fill="rgba(255, 69, 0, 0.7)" />
              <circle cx="2" cy="2" r="1" fill="rgba(255, 255, 255, 0.4)" />
            </pattern>
            <rect width="150" height="150" fill="url(#login-dots)" />
          </svg>
        </div>
        <div class="login-graphic lines">
          <svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
            <line x1="0" y1="40" x2="200" y2="40" stroke="rgba(255, 69, 0, 0.5)" stroke-width="2.5" />
            <line x1="0" y1="80" x2="200" y2="80" stroke="rgba(255, 107, 26, 0.5)" stroke-width="2.5" />
            <line x1="0" y1="120" x2="200" y2="120" stroke="rgba(255, 45, 0, 0.5)" stroke-width="2.5" />
            <line x1="0" y1="160" x2="200" y2="160" stroke="rgba(204, 55, 0, 0.5)" stroke-width="2.5" />
          </svg>
        </div>
      </div>

      <div class="container">
        <h2 class="section-title">เข้าสู่ระบบของเรา</h2>
        <p class="section-subtitle">เข้าถึงระบบจัดการงานและบริการต่างๆ ของเราได้อย่างสะดวกรวดเร็วผ่านระบบล็อกอินที่ปลอดภัย</p>
        <LoginSystem />
      </div>
    </section>
    <Contact />
  </main>
  <Footer />
</Layout>

<style>
  main {
    width: 100%;
  }

  .login-section {
    background: linear-gradient(135deg, #ff8c42 0%, #ff6b1a 25%, #ff4500 75%, #cc3700 100%);
    position: relative;
    overflow: hidden;
    background-image:
      radial-gradient(circle at 10% 20%, rgba(255, 69, 0, 0.4) 0%, transparent 50%),
      radial-gradient(circle at 90% 80%, rgba(255, 107, 26, 0.4) 0%, transparent 50%),
      radial-gradient(circle at 50% 50%, rgba(139, 69, 19, 0.3) 0%, transparent 60%),
      linear-gradient(135deg, rgba(255, 45, 0, 0.2) 0%, rgba(204, 55, 0, 0.2) 100%);
    box-shadow: inset 0 0 100px rgba(0, 0, 0, 0.2);
  }

  /* Login graphics styles */
  .login-graphics {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 0;
  }

  .login-graphic {
    position: absolute;
    opacity: 0.95;
  }

  .shape1 {
    top: 15%;
    right: 10%;
    width: 110px;
    height: 110px;
    background: linear-gradient(135deg, rgba(255, 69, 0, 0.7), rgba(255, 107, 26, 0.7), rgba(139, 69, 19, 0.5));
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    animation: morph 15s ease-in-out infinite, intensePulse 4s ease-in-out infinite alternate;
    box-shadow:
      0 0 30px rgba(255, 69, 0, 0.6),
      0 0 60px rgba(255, 107, 26, 0.4),
      inset 0 0 20px rgba(255, 255, 255, 0.2);
  }

  .shape2 {
    bottom: 20%;
    left: 8%;
    width: 85px;
    height: 85px;
    background: linear-gradient(45deg, rgba(255, 107, 26, 0.7), rgba(255, 45, 0, 0.7), rgba(160, 82, 45, 0.5));
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
    animation: morph 18s ease-in-out infinite alternate, intensePulse 3.5s ease-in-out infinite alternate;
    box-shadow:
      0 0 30px rgba(255, 107, 26, 0.6),
      0 0 60px rgba(255, 45, 0, 0.4),
      inset 0 0 20px rgba(255, 255, 255, 0.2);
  }

  .shape3 {
    top: 60%;
    right: 15%;
    width: 65px;
    height: 65px;
    background: linear-gradient(to right, rgba(255, 69, 0, 0.6), rgba(255, 107, 26, 0.6), rgba(204, 55, 0, 0.4));
    border-radius: 50%;
    animation: float 12s ease-in-out infinite, intensePulse 5s ease-in-out infinite alternate;
    box-shadow:
      0 0 25px rgba(255, 69, 0, 0.5),
      0 0 50px rgba(255, 107, 26, 0.3),
      inset 0 0 15px rgba(255, 255, 255, 0.2);
  }

  .shape4 {
    top: 30%;
    left: 15%;
    width: 75px;
    height: 75px;
    background: linear-gradient(to bottom, rgba(255, 69, 0, 0.6), rgba(255, 107, 26, 0.6), rgba(139, 69, 19, 0.4));
    transform: rotate(45deg);
    animation: rotate 20s linear infinite, intensePulse 4.5s ease-in-out infinite alternate;
    box-shadow:
      0 0 25px rgba(255, 69, 0, 0.5),
      0 0 50px rgba(255, 107, 26, 0.3),
      inset 0 0 15px rgba(255, 255, 255, 0.2);
  }

  @keyframes intensePulse {
    0% {
      transform: scale(1);
      opacity: 0.8;
    }
    100% {
      transform: scale(1.15);
      opacity: 1;
    }
  }

  .dots {
    bottom: 10%;
    right: 20%;
    animation: float 15s ease-in-out infinite;
  }

  .lines {
    top: 5%;
    left: 5%;
    opacity: 0.5;
    animation: float 20s ease-in-out infinite reverse;
  }

  @keyframes morph {
    0%, 100% {
      border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    }
    25% {
      border-radius: 70% 30% 30% 70% / 70% 70% 30% 30%;
    }
    50% {
      border-radius: 30% 70% 70% 30% / 70% 30% 70% 30%;
    }
    75% {
      border-radius: 70% 30% 30% 70% / 30% 70% 30% 70%;
    }
  }

  @keyframes rotate {
    0% { transform: rotate(45deg); }
    100% { transform: rotate(405deg); }
  }

  .login-section::before {
    content: '';
    position: absolute;
    top: -50px;
    right: -50px;
    width: 350px;
    height: 350px;
    background: radial-gradient(circle, rgba(255, 69, 0, 0.6) 0%, rgba(255, 107, 26, 0.4) 40%, transparent 70%);
    border-radius: 50%;
    z-index: -1;
    animation: float 15s ease-in-out infinite, intenseGlow 6s ease-in-out infinite alternate;
    pointer-events: none;
    filter: blur(3px);
    box-shadow:
      inset 0 0 50px rgba(255, 69, 0, 0.4),
      0 0 80px rgba(255, 69, 0, 0.3);
  }

  .login-section::after {
    content: '';
    position: absolute;
    bottom: -50px;
    left: -50px;
    width: 350px;
    height: 350px;
    background: radial-gradient(circle, rgba(255, 107, 26, 0.6) 0%, rgba(255, 45, 0, 0.4) 40%, transparent 70%);
    border-radius: 50%;
    z-index: -1;
    animation: float 18s ease-in-out infinite reverse, intenseGlow 7s ease-in-out infinite alternate;
    pointer-events: none;
    filter: blur(3px);
    box-shadow:
      inset 0 0 50px rgba(255, 107, 26, 0.4),
      0 0 80px rgba(255, 107, 26, 0.3);
  }

  @keyframes intenseGlow {
    0% {
      opacity: 0.7;
      transform: scale(1);
    }
    100% {
      opacity: 1;
      transform: scale(1.1);
    }
  }

  /* Enhanced additional decorative elements */
  .login-section .container::before {
    content: '';
    position: absolute;
    top: 20%;
    left: 5%;
    width: 180px;
    height: 180px;
    background: radial-gradient(circle, rgba(255, 45, 0, 0.5) 0%, rgba(255, 69, 0, 0.3) 50%, transparent 80%);
    border-radius: 50%;
    z-index: -1;
    animation: float 12s ease-in-out infinite, intenseGlow 5s ease-in-out infinite alternate;
    pointer-events: none;
    filter: blur(2px);
    box-shadow:
      inset 0 0 40px rgba(255, 45, 0, 0.3),
      0 0 60px rgba(255, 45, 0, 0.2);
  }

  .login-section .container::after {
    content: '';
    position: absolute;
    bottom: 15%;
    right: 8%;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(255, 69, 0, 0.5) 0%, rgba(255, 107, 26, 0.3) 50%, transparent 80%);
    border-radius: 50%;
    z-index: -1;
    animation: float 20s ease-in-out infinite reverse, intenseGlow 6s ease-in-out infinite alternate;
    pointer-events: none;
    filter: blur(2px);
    box-shadow:
      inset 0 0 40px rgba(255, 69, 0, 0.3),
      0 0 60px rgba(255, 69, 0, 0.2);
  }

  @keyframes float {
    0% {
      transform: translate(0, 0) scale(1);
      opacity: 0.8;
    }
    25% {
      transform: translate(8px, 12px) scale(1.03);
      opacity: 1;
    }
    50% {
      transform: translate(15px, 15px) scale(1.07);
      opacity: 0.9;
    }
    75% {
      transform: translate(8px, 3px) scale(1.05);
      opacity: 0.95;
    }
    100% {
      transform: translate(0, 0) scale(1);
      opacity: 0.8;
    }
  }
</style>