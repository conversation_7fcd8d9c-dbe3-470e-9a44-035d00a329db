---
---

<div class="production-video-container">
  <div class="video-wrapper">
    <iframe
      id="production-video"
      class="production-video"
      src="https://www.youtube.com/embed/SM6A4Og_7Jc?rel=0&modestbranding=1&controls=1&showinfo=0&enablejsapi=1&autoplay=1&mute=1&loop=1&playlist=SM6A4Og_7Jc&playsinline=1&start=0"
      title="EP2 VDO production หนังสือเตือน"
      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
      allowfullscreen
    ></iframe>

    <div class="video-controls">
      <button id="play-pause-btn" class="control-btn" aria-label="Pause video">
        <svg xmlns="http://www.w3.org/2000/svg" class="pause-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <rect x="6" y="4" width="4" height="16"></rect>
          <rect x="14" y="4" width="4" height="16"></rect>
        </svg>
        <svg xmlns="http://www.w3.org/2000/svg" class="play-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="display: none;">
          <polygon points="5 3 19 12 5 21 5 3"></polygon>
        </svg>
      </button>

      <button id="sound-toggle-btn" class="sound-toggle-btn" aria-label="Toggle sound">
        <!-- Muted icon (default) -->
        <svg xmlns="http://www.w3.org/2000/svg" class="muted-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M11 5L6 9H2v6h4l5 4zM22 9l-6 6M16 9l6 6"></path>
        </svg>
        <!-- Unmuted icon (hidden by default) -->
        <svg xmlns="http://www.w3.org/2000/svg" class="unmuted-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="display: none;">
          <polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon>
          <path d="M15.54 8.46a5 5 0 0 1 0 7.07"></path>
          <path d="M19.07 4.93a10 10 0 0 1 0 14.14"></path>
        </svg>
        <span id="sound-text">Sound Off</span>
      </button>
    </div>
  </div>
</div>

<style>
  .production-video-container {
    width: 100%;
    max-width: 800px;
    margin: 2rem auto;
    border-radius: 16px;
    overflow: hidden;
    position: relative;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2), 0 0 20px rgba(255, 123, 0, 0.2);
    background: linear-gradient(135deg, rgba(255, 123, 0, 0.1), rgba(255, 158, 0, 0.1));
    padding: 4px;
    border: 2px solid rgba(255, 123, 0, 0.2);
    z-index: 2; /* Ensure video is above decorative elements */
  }

  .video-wrapper {
    position: relative;
    width: 100%;
    padding-bottom: 56.25%; /* 16:9 Aspect Ratio */
    border-radius: 12px;
    overflow: hidden;
  }

  .production-video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
  }

  .video-controls {
    position: absolute;
    bottom: 15px;
    right: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
    z-index: 10;
  }

  .control-btn {
    background: rgba(0, 0, 0, 0.6);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: white;
    transition: all 0.3s ease;
  }

  .control-btn:hover {
    background: rgba(0, 0, 0, 0.8);
    transform: scale(1.1);
  }

  .control-btn svg {
    width: 20px;
    height: 20px;
  }

  .sound-toggle-btn {
    background: rgba(0, 0, 0, 0.6);
    color: white;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 5px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .sound-toggle-btn:hover {
    background: rgba(0, 0, 0, 0.8);
    transform: scale(1.05);
  }

  .sound-toggle-btn svg {
    width: 16px;
    height: 16px;
  }

  .sound-toggle-btn.unmuted {
    background: rgba(255, 123, 0, 0.8);
  }

  /* Custom styles to hide YouTube icons */
  :global(.ytp-chrome-top) {
    display: none !important;
  }

  :global(.ytp-chrome-bottom .ytp-youtube-button) {
    display: none !important;
  }

  :global(.ytp-watermark) {
    display: none !important;
  }

  @media (max-width: 768px) {
    .production-video-container {
      margin: 1.5rem auto;
      max-width: 90%;
    }

    .video-controls {
      bottom: 10px;
      right: 10px;
    }

    .control-btn {
      width: 36px;
      height: 36px;
    }

    .sound-toggle-btn {
      font-size: 10px;
      padding: 4px 8px;
    }

    .sound-toggle-btn svg {
      width: 14px;
      height: 14px;
    }
  }
</style>

<script>
  // Define YouTube API types
  declare global {
    interface Window {
      onYouTubeIframeAPIReady: () => void;
      YT: {
        Player: any;
        PlayerState: {
          ENDED: number;
          PLAYING: number;
          PAUSED: number;
          BUFFERING: number;
          CUED: number;
        };
      };
    }
  }

  // Load YouTube API
  let tag = document.createElement('script');
  tag.src = "https://www.youtube.com/iframe_api";
  let firstScriptTag = document.getElementsByTagName('script')[0];
  if (firstScriptTag && firstScriptTag.parentNode) {
    firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);
  }

  // Variable to hold the player instance
  let player: any = null;

  // This function will be called by the YouTube API
  window.onYouTubeIframeAPIReady = function() {
    player = new window.YT.Player('production-video', {
      events: {
        'onReady': onPlayerReady,
        'onStateChange': onPlayerStateChange
      }
    });
  };

  function onPlayerReady(event: any) {
    // Make sure the video is muted and then play from the beginning
    event.target.mute();
    event.target.seekTo(0); // Start from the beginning
    event.target.playVideo();

    // Force play after a short delay (helps with some browsers)
    setTimeout(() => {
      if (player && typeof player.playVideo === 'function') {
        player.playVideo();
      }
    }, 100);

    // Update UI to reflect muted state
    const soundToggleBtn = document.getElementById('sound-toggle-btn');
    const mutedIcon = document.querySelector('.muted-icon') as HTMLElement;
    const unmutedIcon = document.querySelector('.unmuted-icon') as HTMLElement;
    const soundText = document.getElementById('sound-text');

    if (mutedIcon) mutedIcon.style.display = 'block';
    if (unmutedIcon) unmutedIcon.style.display = 'none';
    if (soundText) soundText.textContent = 'Sound Off';
    if (soundToggleBtn) {
      soundToggleBtn.classList.remove('unmuted');
      soundToggleBtn.setAttribute('aria-label', 'Unmute video');
    }
  }

  function onPlayerStateChange(event: any) {
    // If video ends, restart it (for loop functionality)
    if (event.data === window.YT.PlayerState.ENDED) {
      event.target.playVideo();
    }
  }

  // Handle visibility changes to pause/play the video when tab is not visible
  document.addEventListener('visibilitychange', function() {
    if (document.hidden && player && typeof player.pauseVideo === 'function') {
      player.pauseVideo();
    } else if (!document.hidden && player && typeof player.playVideo === 'function') {
      player.playVideo();
    }
  });

  // Handle play/pause button and ensure video is muted
  document.addEventListener('DOMContentLoaded', () => {
    const videoContainer = document.querySelector('.production-video-container');
    const playPauseBtn = document.getElementById('play-pause-btn');
    const playIcon = document.querySelector('.play-icon') as HTMLElement;
    const pauseIcon = document.querySelector('.pause-icon') as HTMLElement;

    // Initialize button state
    let isPlaying = true;

    // Function to update player state
    function updatePlayerState(state: number) {
      if (!playIcon || !pauseIcon || !playPauseBtn) return;

      if (state === window.YT.PlayerState.PLAYING) {
        isPlaying = true;
        if (pauseIcon) pauseIcon.style.display = 'block';
        if (playIcon) playIcon.style.display = 'none';
        playPauseBtn.setAttribute('aria-label', 'Pause video');
      } else if (state === window.YT.PlayerState.PAUSED || state === window.YT.PlayerState.ENDED) {
        isPlaying = false;
        if (pauseIcon) pauseIcon.style.display = 'none';
        if (playIcon) playIcon.style.display = 'block';
        playPauseBtn.setAttribute('aria-label', 'Play video');
      }
    }

    if (playPauseBtn) {
      playPauseBtn.addEventListener('click', () => {
        if (!player) return;

        if (isPlaying) {
          // Pause the video
          player.pauseVideo();
          if (pauseIcon) pauseIcon.style.display = 'none';
          if (playIcon) playIcon.style.display = 'block';
          playPauseBtn.setAttribute('aria-label', 'Play video');
        } else {
          // Play the video
          player.playVideo();
          if (pauseIcon) pauseIcon.style.display = 'block';
          if (playIcon) playIcon.style.display = 'none';
          playPauseBtn.setAttribute('aria-label', 'Pause video');
        }

        isPlaying = !isPlaying;
      });
    }

    // Override the onPlayerStateChange function to include our UI updates
    const originalOnPlayerStateChange = onPlayerStateChange;

    // Create a new function that will replace the original
    function newOnPlayerStateChange(event: any) {
      // Call the original function
      originalOnPlayerStateChange(event);

      // Update our UI
      updatePlayerState(event.data);
    }

    // Replace the global function
    window.onYouTubeIframeAPIReady = function() {
      player = new window.YT.Player('production-video', {
        events: {
          'onReady': onPlayerReady,
          'onStateChange': newOnPlayerStateChange
        }
      });
    };

    // Handle sound toggle button
    const soundToggleBtn = document.getElementById('sound-toggle-btn');
    const mutedIcon = document.querySelector('.muted-icon') as HTMLElement;
    const unmutedIcon = document.querySelector('.unmuted-icon') as HTMLElement;
    const soundText = document.getElementById('sound-text');

    // Initialize sound state (muted by default)
    let isMuted = true;

    if (soundToggleBtn) {
      soundToggleBtn.addEventListener('click', () => {
        if (!player) return;

        if (isMuted) {
          // Unmute the video
          player.unMute();
          if (mutedIcon) mutedIcon.style.display = 'none';
          if (unmutedIcon) unmutedIcon.style.display = 'block';
          if (soundText) soundText.textContent = 'Sound On';
          soundToggleBtn.classList.add('unmuted');
          soundToggleBtn.setAttribute('aria-label', 'Mute video');
        } else {
          // Mute the video
          player.mute();
          if (mutedIcon) mutedIcon.style.display = 'block';
          if (unmutedIcon) unmutedIcon.style.display = 'none';
          if (soundText) soundText.textContent = 'Sound Off';
          soundToggleBtn.classList.remove('unmuted');
          soundToggleBtn.setAttribute('aria-label', 'Unmute video');
        }

        isMuted = !isMuted;
      });
    }

    // Ensure video is muted when clicking on the video (but not on control buttons)
    if (videoContainer) {
      videoContainer.addEventListener('click', (e: Event) => {
        const target = e.target as HTMLElement;

        // Don't handle clicks on the control buttons
        if (target === playPauseBtn ||
            (playPauseBtn && target instanceof Node && playPauseBtn.contains(target)) ||
            target === soundToggleBtn ||
            (soundToggleBtn && target instanceof Node && soundToggleBtn.contains(target))) {
          return;
        }

        // Only force mute if user clicks directly on the video
        if (player && typeof player.mute === 'function' && isMuted) {
          player.mute();
        }
      });
    }
  });
</script>
