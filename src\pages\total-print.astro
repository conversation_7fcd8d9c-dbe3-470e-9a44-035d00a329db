---
import Layout from '../layouts/Layout.astro';
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';
import LogoSlideshow from '../components/LogoSlideshow.astro';
---

<Layout title="Jupiter Tech - บริการงานพิมพ์จดหมายในระบบดิจิตอล (Total Printing)">
  <Header />
  <main>
    <section class="hero-section">
      <div class="container">
        <div class="hero-content">
          <div class="service-icon">
            <img src="/images/total-print-icon.svg" alt="Total Printing Icon" />
          </div>
          <h1 class="page-title">Total Printing</h1>
          <p class="hero-description">
            เป็นการให้บริการงานพิมพ์ข้อมูลลูกค้าด้วยระบบ Files to Print เป็นการพิมพ์ที่ใช้ Data มาทำการจัดข้อมูลตามแบบที่ลูกค้าต้องการแล้วส่งพิมพ์ในระบบการพิมพ์ดิจิตอล
          </p>

          <ul class="feature-list">
            <li>
              <span class="check-icon">✓</span>
              <span>โครงการพิมพ์ภาษีที่ดินและสิ่งปลูกสร้าง</span>
            </li>
            <li>
              <span class="check-icon">✓</span>
              <span>พร้อมจัดส่งไปรษณีย์</span>
            </li>
            <li>
              <span class="check-icon">✓</span>
              <span>ระบบการพิมพ์สีและขาวดำ</span>
            </li>
            <li>
              <span class="check-icon">✓</span>
              <span>ป้องกันความผิดพลาดได้ทั้งหมด (Zero defect)</span>
            </li>
          </ul>

          <a href="/total-print#services" class="more-details-link">
            รายละเอียดเพิ่มเติม
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M5 12h14M12 5l7 7-7 7"/>
            </svg>
          </a>
        </div>
        <div class="hero-image">
          <img src="/images/Total-Printing2.png" alt="Total Printing Service" />
        </div>
      </div>
      <div class="shape-divider">
        <svg data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none">
          <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z" class="shape-fill"></path>
        </svg>
      </div>
    </section>

    <LogoSlideshow />

    <section class="services-section">
      <div class="container">
        <h2 class="section-title">บริการของเรา</h2>

        <div class="service-cards">
          <div class="service-card">
            <div class="service-icon">
              <img src="/images/SFTP/sftp1.png" alt="SFTP Icon" />
            </div>
            <h3>ช่องทางการรับส่งข้อมูล SFTP</h3>
            <p class="service-subtitle">(Secure File Transfer Protocol)</p>
            <ul class="service-features">
              <li>กำหนดรหัส "ความปลอดภัย" ( Key, User, Password) ให้ลูกค้า</li>
              <li>จัดส่งคู่มือการใช้งานให้ลูกค้า</li>
              <li>กำหนดเครื่องที่ใช้งาน และ เบอร์ IP Address เฉพาะของ ลูกค้า</li>
            </ul>
          </div>

          <div class="service-card">
            <div class="service-icon">
              <img src="/images/SFTP/sftp2.png" alt="Web Network Security Icon" />
            </div>
            <h3>ช่องทางการรับส่งข้อมูล</h3>
            <p class="service-subtitle">(Web Network Security)</p>
            <ul class="service-features">
              <li>กำหนดรหัส "ความปลอดภัย" และกำหนดสิทธิ์การใช้งาน</li>
              <li>กำหนดเครื่องต้องใช้งานผ่าน Internet ได้</li>
              <li>กำหนด Encryption file งานข้อมูล</li>
            </ul>
          </div>

          <div class="service-card">
            <div class="service-icon">
              <img src="/images/products/Dev1.png" alt="Development Program Icon" />
            </div>
            <h3>การพัฒนาเทมเพลทและโปรแกรม</h3>
            <p class="service-subtitle">(Development Program)</p>
            <ul class="service-features">
              <li>รับตัวอย่างข้อมูล Text file</li>
              <li>รับรายละเอียดตัวอย่างงาน (Template)</li>
              <li>รับ Data Mapping (File กำหนดการเชื่อมข้อมูลใช้กับ Template)</li>
              <li>พัฒนาเทมเพลท และสร้างรูปแบบฟอร์ม และโปรแกรมตามเงื่อนไข ลูกค้า กำหนด</li>
            </ul>
          </div>

          <div class="service-card">
            <div class="service-icon">
              <img src="/images/products/Print2.png" alt="Letter Production Icon" />
            </div>
            <h3>การผลิตงานจดหมาย</h3>
            <p class="service-subtitle">(Letter Production)</p>
            <ul class="service-features">
              <li>ผลิตงานตามเงื่อนไขข้อมูลในการแยก Hub และแยกรหัสไปรษณีย์</li>
              <li>จัดพิมพ์บนเงื่อนไข ประเภทงาน Sealer ออกจากกันอย่างชัดเจน</li>
              <li>ทำตามระยะเวลาในการทำงาน (SLA)</li>
              <li>มีการควบคุมงานด้วยทีม QC/QA เมื่อมีการผลิตงานเสร็จ</li>
            </ul>
          </div>
        </div>
      </div>
    </section>

    <section class="process-section">
      <div class="container">
        <h2 class="section-title">กระบวนการทำงาน</h2>

        <div class="process-timeline">
          <div class="timeline-item">
            <div class="timeline-number">1</div>
            <div class="timeline-content">
              <h3>การพัฒนาเทมเพลทและโปรแกรม</h3>
              <p>เริ่มต้นจากการรับข้อมูลและความต้องการจากลูกค้า เพื่อพัฒนาเทมเพลทและโปรแกรมที่ตรงตามความต้องการ</p>
            </div>
          </div>

          <div class="timeline-item">
            <div class="timeline-number">2</div>
            <div class="timeline-content">
              <h3>การทดสอบระบบ (UAT Process)</h3>
              <p>ทดสอบระบบร่วมกับลูกค้าเพื่อให้มั่นใจว่าผลลัพธ์ตรงตามความต้องการก่อนเริ่มการผลิตจริง</p>
            </div>
          </div>

          <div class="timeline-item">
            <div class="timeline-number">3</div>
            <div class="timeline-content">
              <h3>การผลิตงานจดหมาย</h3>
              <p>ดำเนินการผลิตงานตามเงื่อนไขที่กำหนด ด้วยระบบการควบคุมคุณภาพที่เข้มงวด</p>
            </div>
          </div>

          <div class="timeline-item">
            <div class="timeline-number">4</div>
            <div class="timeline-content">
              <h3>การจัดส่งโดยบริษัท ไปรษณีย์ไทย จำกัด</h3>
              <p>ประสานงานกับบริษัท ไปรษณีย์ไทย จำกัด เพื่อจัดส่งเอกสารให้ถึงมือผู้รับอย่างรวดเร็วและปลอดภัย</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="cta-section">
      <div class="container">
        <h2>สนใจเข้าใช้บริการโครงการพิมพ์ภาษีที่ดินและสิ่งปลูกสร้างพร้อมจัดส่งไปรษณีย์</h2>
        <div class="cta-buttons">
          <a href="/doc/เอกสารรักษาความลับข้อมูลลูกค้า NDA.docx" class="btn btn-primary">ดาวน์โหลดเอกสาร NDA</a>
          <a href="/doc/โครงการพิมพ์พร้อมส่ง ไปรษณีย์.pdf" class="btn btn-outline">ดาวน์โหลดเอกสาร ค่าบริการโครงการ</a>
        </div>
      </div>
    </section>
  </main>
  <Footer />
</Layout>

<style>
  .hero-section {
    position: relative;
    padding: 8rem 0 6rem;
    background: linear-gradient(135deg, var(--light-background) 0%, #ffe0c2 100%);
    overflow: hidden;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    align-items: center;
  }

  .service-icon {
    width: 100px;
    height: 100px;
    margin-bottom: 1.5rem;
  }

  .service-icon img {
    width: 100%;
    height: 100%;
  }

  .page-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #333;
  }

  .hero-description {
    font-size: 1.1rem;
    line-height: 1.6;
    color: #666;
    margin-bottom: 2rem;
  }

  .feature-list {
    list-style-type: none;
    padding: 0;
    margin: 0 0 2rem 0;
  }

  .feature-list li {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1rem;
  }

  .check-icon {
    color: #FF7B00;
    font-size: 1.2rem;
    margin-right: 0.8rem;
    font-weight: bold;
  }

  .more-details-link {
    display: inline-flex;
    align-items: center;
    color: #FF7B00;
    font-weight: 600;
    text-decoration: none;
    transition: color 0.3s ease;
  }

  .more-details-link svg {
    margin-left: 0.5rem;
    transition: transform 0.3s ease;
  }

  .more-details-link:hover {
    color: #E56A00;
  }

  .more-details-link:hover svg {
    transform: translateX(3px);
  }

  .hero-image img {
    width: 100%;
    height: auto;
    border-radius: 10px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  }

  .shape-divider {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    overflow: hidden;
    line-height: 0;
  }

  .shape-divider svg {
    position: relative;
    display: block;
    width: calc(100% + 1.3px);
    height: 80px;
  }

  .shape-divider .shape-fill {
    fill: var(--background-color);
  }

  /* Services Section */
  .services-section {
    padding: 5rem 0;
    background-color: var(--background-color);
  }

  .services-section .container {
    display: block;
  }

  .section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 3rem;
    color: var(--text-color);
  }

  .service-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 2rem;
  }

  .service-card {
    background-color: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(255, 123, 0, 0.15);
    border-left: 4px solid var(--accent-color);
  }

  .service-icon {
    width: 80px;
    height: 80px;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .service-icon img {
    max-width: 100%;
    max-height: 100%;
  }

  .service-card h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--text-color);
  }

  .service-subtitle {
    font-size: 1rem;
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
  }

  .service-features {
    list-style-type: none;
    padding: 0;
    margin: 0;
  }

  .service-features li {
    position: relative;
    padding-left: 1.5rem;
    margin-bottom: 0.8rem;
    line-height: 1.4;
  }

  .service-features li::before {
    content: "";
    position: absolute;
    left: 0;
    top: 8px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--primary-color);
  }

  /* Process Section */
  .process-section {
    padding: 5rem 0;
    background-color: var(--light-background);
  }

  .process-section .container {
    display: block;
  }

  .process-timeline {
    max-width: 800px;
    margin: 0 auto;
    position: relative;
  }

  .process-timeline::before {
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    left: 50px;
    width: 4px;
    background: linear-gradient(to bottom, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
  }

  .timeline-item {
    display: flex;
    margin-bottom: 3rem;
    position: relative;
  }

  .timeline-number {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 1.5rem;
    margin-right: 2rem;
    z-index: 2;
    box-shadow: 0 5px 15px rgba(255, 123, 0, 0.3);
  }

  .timeline-content {
    background-color: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    flex: 1;
  }

  .timeline-content h3 {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--text-color);
  }

  .timeline-content p {
    margin: 0;
    color: var(--text-secondary);
    line-height: 1.6;
  }

  /* CTA Section */
  .cta-section {
    padding: 5rem 0;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    text-align: center;
    color: white;
  }

  .cta-section .container {
    display: block;
  }

  .cta-section h2 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 2rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
  }

  .cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
  }

  .btn {
    display: inline-block;
    padding: 0.8rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
  }

  .btn-primary {
    background-color: white;
    color: var(--primary-color);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  .btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  }

  .btn-outline {
    background: transparent;
    color: white;
    border: 2px solid white;
  }

  .btn-outline:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-3px);
  }

  @media (max-width: 768px) {
    .container {
      grid-template-columns: 1fr;
    }

    .hero-section {
      padding: 6rem 0 4rem;
    }

    .page-title {
      font-size: 2.5rem;
    }

    .hero-image {
      margin-top: 2rem;
    }

    .process-timeline::before {
      left: 25px;
    }

    .timeline-number {
      width: 40px;
      height: 40px;
      font-size: 1.2rem;
      margin-right: 1.5rem;
    }
  }
</style>
