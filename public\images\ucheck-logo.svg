<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
  <defs>
    <linearGradient id="ucheck-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#FFA500" />
      <stop offset="100%" stop-color="#FF5733" />
    </linearGradient>
  </defs>
  <rect width="100" height="100" rx="10" fill="url(#ucheck-gradient)" />

  <!-- U letter -->
  <path d="M35 20 C35 20, 35 50, 35 60 C35 68, 42 75, 50 75 C58 75, 65 68, 65 60 C65 50, 65 20, 65 20"
        stroke="white" stroke-width="14" stroke-linecap="round" fill="none" />

  <!-- Horizontal line under U -->
  <path d="M25 60 C25 60, 50 68, 75 60"
        stroke="white" stroke-width="4" stroke-linecap="round" fill="none" />

  <!-- Check text -->
  <text x="50" y="92" font-family="Arial, sans-serif" font-size="22" font-weight="bold"
        fill="white" text-anchor="middle">Check</text>
</svg>
