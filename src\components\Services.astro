---
---

<section id="services" class="section services">
  <!-- Decorative graphics -->
  <div class="decorative-graphics">
    <div class="graphic-circle circle1"></div>
    <div class="graphic-circle circle2"></div>
    <div class="graphic-square"></div>
    <div class="graphic-dots">
      <svg width="120" height="120" viewBox="0 0 120 120" xmlns="http://www.w3.org/2000/svg">
        <pattern id="dots-pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
          <circle cx="3" cy="3" r="2" fill="rgba(255, 123, 0, 0.2)" />
        </pattern>
        <rect width="120" height="120" fill="url(#dots-pattern)" />
      </svg>
    </div>
    <div class="graphic-wave">
      <svg width="200" height="50" viewBox="0 0 200 50" xmlns="http://www.w3.org/2000/svg">
        <path d="M0,25 C20,40 40,10 60,25 C80,40 100,10 120,25 C140,40 160,10 180,25 C200,40 220,10 240,25" stroke="rgba(255, 123, 0, 0.2)" stroke-width="2" fill="none" />
      </svg>
    </div>
  </div>

  <div class="container">
    <h2 class="section-title">ผลิตภัณฑ์ของเรา</h2>
    <p class="section-subtitle">บริการครบวงจรที่ตอบโจทย์ความต้องการของธุรกิจคุณ</p>

    <div class="services-container">
      <div class="service-card">
        <div class="service-icon">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M6.72 13.829c-.24.03-.48.062-.72.096m.72-.096a42.415 42.415 0 0110.56 0m-10.56 0L6.34 18m10.94-4.171c.24.03.48.062.72.096m-.72-.096L17.66 18m0 0l.229 2.523a1.125 1.125 0 01-1.12 1.227H7.231c-.662 0-1.18-.568-1.12-1.227L6.34 18m11.318 0h1.091A2.25 2.25 0 0021 15.75V9.456c0-1.081-.768-2.015-1.837-2.175a48.055 48.055 0 00-1.913-.247M6.34 18H5.25A2.25 2.25 0 013 15.75V9.456c0-1.081.768-2.015 1.837-2.175a48.041 48.041 0 011.913-.247m10.5 0a48.536 48.536 0 00-10.5 0m10.5 0V3.375c0-.621-.504-1.125-1.125-1.125h-8.25c-.621 0-1.125.504-1.125 1.125v3.659M18 10.5h.008v.008H18V10.5zm-3 0h.008v.008H15V10.5z" />
          </svg>
        </div>
        <h3>Total Printing</h3>
        <p>เป็นการให้บริการงานพิมพ์ข้อมูลลูกค้าด้วยระบบ Files to Print เป็นการพิมพ์ที่ใช้ Data มาทำการจัดข้อมูลตามแบบที่ลูกค้าต้องการแล้วส่งพิมพ์ในระบบการพิมพ์ดิจิตอล</p>
        <ul class="service-features">
          <li>โครงการพิมพ์ภาษีที่ดินและสิ่งปลูกสร้าง</li>
          <li>พร้อมจัดส่งไปรษณีย์</li>
          <li>ระบบการพิมพ์สีและขาวดำ</li>
          <li>ป้องกันความผิดพลาดได้ทั้งหมด (Zero defect)</li>
        </ul>
        <a href="https://jupitertech.co.th/page/total-print/" class="service-link">รายละเอียดเพิ่มเติม</a>
      </div>

      <div class="service-card">
        <div class="service-icon">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 17.25v1.007a3 3 0 01-.879 2.122L7.5 21h9l-.621-.621A3 3 0 0115 18.257V17.25m6-12V15a2.25 2.25 0 01-2.25 2.25H5.25A2.25 2.25 0 013 15V5.25m18 0A2.25 2.25 0 0018.75 3H5.25A2.25 2.25 0 003 5.25m18 0V12a2.25 2.25 0 01-2.25 2.25H5.25A2.25 2.25 0 013 12V5.25" />
          </svg>
        </div>
        <h3>Platform Tigra</h3>
        <p>Platform Tigra เป็นโปรแกรมจัดเก็บค่าธรรมเนียมเก็บขนขยะมูลฝอยและสิ่งปฏิกูล โดยมีจุดเด่น คือ สามารถตั้งหนี้จัดเก็บประจำปี ตั้งค่าโซนการจัดเก็บฯ เช่น ชุมชน หมู่บ้าน</p>
        <ul class="service-features">
          <li>ออกใบแจ้งหนี้ผ่านช่องทาง Bill payment</li>
          <li>แจ้งเตือนเจ้าหน้าที่อัตโนมัติ</li>
          <li>ออกใบเสร็จรับเงิน หรือ ยกเลิกใบเสร็จได้ทันที</li>
          <li>ระบบหน้า Dashboard รวม</li>
        </ul>
        <a href="https://thaigarbage.com" class="service-link">รายละเอียดเพิ่มเติม</a>
      </div>

      <div class="service-card">
        <div class="service-icon">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9.568 3H5.25A2.25 2.25 0 003 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 005.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 009.568 3z" />
            <path stroke-linecap="round" stroke-linejoin="round" d="M6 6h.008v.008H6V6z" />
          </svg>
        </div>
        <h3>Advanced Label</h3>
        <p>Advanced Label เป็นการผลิตงานป้ายแสดงสถานะชนิดมีแผ่นรองหลัง (liner and lineless Label) ผลิตภัณฑ์ดังกล่าวเหมาะติดลงบนผลิตภัณฑ์ประเภท กล่อง บรรจุผลิตภัณฑ์ ถุงทุกชนิด</p>
        <ul class="service-features">
          <li>ป้ายแสดงสถานะป้องกันการปลอมแปลง</li>
          <li>บรรจุ QR Code</li>
          <li>การใส่ข้อมูลแบบ Variable Data</li>
          <li>ติดตามและตรวจสอบผลิตภัณฑ์</li>
        </ul>
        <a href="#contact" class="service-link">รายละเอียดเพิ่มเติม</a>
      </div>

      <div class="service-card">
        <div class="service-icon">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 01-1.043 3.296 3.745 3.745 0 01-3.296 1.043A3.745 3.745 0 0112 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 01-3.296-1.043 3.745 3.745 0 01-1.043-3.296A3.745 3.745 0 013 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 011.043-3.296 3.746 3.746 0 013.296-1.043A3.746 3.746 0 0112 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 013.296 1.043 3.746 3.746 0 011.043 3.296A3.745 3.745 0 0121 12z" />
          </svg>
        </div>
        <h3>U-Check</h3>
        <p>เป็นเครื่องมือที่รวมทุกๆการเชื่อมโยงของระบบการทำงานต่างๆของสมาชิกให้อยู่ภายในหน้าเว็ปไซต์เดียวกัน โดยใช้งานผ่าน user และ password เดียว</p>
        <ul class="service-features">
          <li>ตรวจสอบเพื่ออนุมัติผลิตงานพิมพ์</li>
          <li>ดาว์โหลดตัวอย่างงาน</li>
          <li>ดาว์โหลดหมายเลขลงทะเบียน</li>
          <li>ติดตามและตรวจสอบขั้นตอนในการทำงาน</li>
        </ul>
        <a href="https://u-check.jupitertech.co.th/" class="service-link">รายละเอียดเพิ่มเติม</a>
      </div>
    </div>
  </div>
</section>

<style>
  .services {
    position: relative;
    overflow: hidden;
    background-color: var(--background-color);
    background-image:
      linear-gradient(to right, rgba(255, 255, 255, 0.97), rgba(255, 255, 255, 0.98)),
      repeating-linear-gradient(45deg, rgba(255, 106, 0, 0.03) 0px, rgba(255, 106, 0, 0.03) 1px, transparent 1px, transparent 10px),
      repeating-linear-gradient(135deg, rgba(255, 69, 0, 0.02) 0px, rgba(255, 69, 0, 0.02) 1px, transparent 1px, transparent 10px);
  }

  /* Decorative graphics styles */
  .decorative-graphics {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 0;
    overflow: hidden;
  }

  .graphic-circle {
    position: absolute;
    border-radius: 50%;
    opacity: 0.15;
  }

  .circle1 {
    top: 10%;
    right: 5%;
    width: 220px;
    height: 220px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    animation: float-slow 15s ease-in-out infinite;
    opacity: 0.2;
    filter: blur(1px);
    box-shadow: 0 0 30px rgba(255, 106, 0, 0.1);
  }

  .circle2 {
    bottom: 15%;
    left: 5%;
    width: 170px;
    height: 170px;
    background: linear-gradient(45deg, var(--secondary-color), var(--accent-color));
    animation: float-slow 18s ease-in-out infinite reverse;
    opacity: 0.2;
    filter: blur(1px);
    box-shadow: 0 0 30px rgba(255, 140, 0, 0.1);
  }

  .graphic-square {
    position: absolute;
    top: 40%;
    right: 15%;
    width: 120px;
    height: 120px;
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
    transform: rotate(45deg);
    opacity: 0.15;
    animation: rotate 20s linear infinite;
    filter: blur(1px);
    box-shadow: 0 0 20px rgba(255, 106, 0, 0.1);
  }

  .graphic-dots {
    position: absolute;
    top: 20%;
    left: 10%;
    opacity: 0.7;
    animation: float-slow 12s ease-in-out infinite;
  }

  .graphic-wave {
    position: absolute;
    bottom: 10%;
    right: 10%;
    opacity: 0.7;
    animation: wave 15s ease-in-out infinite;
  }

  @keyframes float-slow {
    0%, 100% { transform: translate(0, 0); }
    50% { transform: translate(10px, 15px); }
  }

  @keyframes rotate {
    0% { transform: rotate(45deg); }
    100% { transform: rotate(405deg); }
  }

  @keyframes wave {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(10px); }
  }

  .services-container {
    position: relative;
    z-index: 1;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
  }

  .service-card {
    background: linear-gradient(145deg, #ffffff 0%, #fafafa 100%);
    border-radius: 20px;
    padding: 2.5rem;
    box-shadow:
      0 15px 35px rgba(0, 0, 0, 0.1),
      0 5px 15px rgba(0, 0, 0, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 0.9),
      inset 0 -1px 0 rgba(0, 0, 0, 0.05);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
    z-index: 1;
    border: 3px solid rgba(255, 106, 0, 0.3);
    transform: perspective(1000px) rotateX(0deg) rotateY(0deg);
    transform-style: preserve-3d;
  }

  .service-card::before {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    background: linear-gradient(145deg, rgba(255, 106, 0, 0.2), rgba(255, 69, 0, 0.1));
    border-radius: 24px;
    z-index: -2;
    opacity: 0.7;
    transition: opacity 0.3s ease;
  }

  .service-card::after {
    content: '';
    position: absolute;
    top: 8px;
    left: 8px;
    right: 8px;
    bottom: 8px;
    border: 2px solid rgba(255, 106, 0, 0.15);
    border-radius: 16px;
    z-index: -1;
    transition: all 0.3s ease;
    opacity: 0.8;
  }

  .service-card:hover {
    transform: perspective(1000px) rotateX(-5deg) rotateY(5deg) translateY(-15px) translateZ(20px);
    box-shadow:
      0 25px 50px rgba(255, 106, 0, 0.25),
      0 15px 35px rgba(0, 0, 0, 0.15),
      0 5px 15px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 1),
      inset 0 -1px 0 rgba(0, 0, 0, 0.1);
    border: 3px solid rgba(255, 106, 0, 0.6);
    background: linear-gradient(145deg, #ffffff 0%, #f8f8f8 100%);
  }

  .service-card:hover::before {
    opacity: 1;
  }

  .service-card:hover::after {
    border: 2px solid rgba(255, 106, 0, 0.4);
    opacity: 1;
  }

  .service-icon {
    width: 75px;
    height: 75px;
    background: linear-gradient(145deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    position: relative;
    box-shadow:
      0 10px 25px rgba(255, 106, 0, 0.3),
      0 5px 15px rgba(255, 106, 0, 0.2),
      inset 0 2px 0 rgba(255, 255, 255, 0.3),
      inset 0 -2px 0 rgba(0, 0, 0, 0.2);
    z-index: 2;
    transform: translateZ(10px);
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  }

  .service-icon::before {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    opacity: 0.4;
    z-index: -1;
    animation: pulse-icon 2s infinite;
  }

  .service-icon::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 45%;
    height: 45%;
    background: rgba(255, 255, 255, 0.25);
    border-radius: 50%;
    filter: blur(5px);
  }

  .service-icon svg {
    width: 30px;
    height: 30px;
    color: white;
    transition: all 0.3s ease;
  }

  .service-card:hover .service-icon {
    transform: translateZ(20px) scale(1.1) rotateY(10deg);
    box-shadow:
      0 15px 35px rgba(255, 106, 0, 0.4),
      0 8px 20px rgba(255, 106, 0, 0.3),
      inset 0 3px 0 rgba(255, 255, 255, 0.4),
      inset 0 -3px 0 rgba(0, 0, 0, 0.3);
  }

  .service-card:hover .service-icon::before {
    opacity: 1;
  }

  .service-card:hover .service-icon svg {
    transform: scale(1.15);
    filter: drop-shadow(0 0 3px rgba(255, 255, 255, 0.5));
  }

  @keyframes pulse-icon {
    0%, 100% {
      opacity: 0.4;
      transform: scale(1);
    }
    50% {
      opacity: 0.6;
      transform: scale(1.15);
    }
  }

  @keyframes float3D {
    0%, 100% {
      transform: perspective(1000px) rotateX(0deg) rotateY(0deg) translateY(0px);
    }
    50% {
      transform: perspective(1000px) rotateX(1deg) rotateY(1deg) translateY(-2px);
    }
  }

  .service-card {
    animation: float3D 6s ease-in-out infinite;
  }

  .service-card:nth-child(2) {
    animation-delay: 2s;
  }

  .service-card:nth-child(3) {
    animation-delay: 4s;
  }

  .service-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--text-color);
    transform: translateZ(5px);
    transition: all 0.3s ease;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  .service-card p {
    color: var(--light-text-color);
    line-height: 1.6;
    margin-bottom: 1.5rem;
    transform: translateZ(3px);
    transition: all 0.3s ease;
  }

  .service-card:hover h3 {
    transform: translateZ(10px);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  }

  .service-card:hover p {
    transform: translateZ(8px);
  }

  .service-features {
    list-style: none;
    margin-bottom: 1.5rem;
  }

  .service-features li {
    position: relative;
    padding-left: 1.5rem;
    margin-bottom: 0.5rem;
    color: var(--text-color);
  }

  .service-features li::before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-weight: bold;
  }

  .service-link {
    margin-top: auto;
    display: inline-block;
    color: var(--primary-color);
    font-weight: 600;
    text-decoration: none;
    position: relative;
    transition: color 0.3s ease;
  }

  .service-link::after {
    content: "→";
    margin-left: 0.5rem;
    transition: transform 0.3s ease;
    display: inline-block;
  }

  .service-link:hover {
    color: var(--secondary-color);
  }

  .service-link:hover::after {
    transform: translateX(5px);
  }

  @media (max-width: 768px) {
    .services-container {
      grid-template-columns: 1fr;
    }
  }
</style>
